{{ 'section-risk-free-trial.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign section_id = 'risk-free-trial-' | append: section.id
  assign heading_tag = section.settings.heading_tag | default: 'h2'
  assign lazy_load = true
  if section.index <= 2
    assign lazy_load = false
  endif
-%}

<section 
  id="{{ section_id }}"
  class="risk-free-trial-section section"
  style="
    --section-padding-top: {{ section.settings.section_padding_top }}px;
    --section-padding-bottom: {{ section.settings.section_padding_bottom }}px;
    --section-padding-top-mobile: {{ section.settings.section_padding_top_mobile }}px;
    --section-padding-bottom-mobile: {{ section.settings.section_padding_bottom_mobile }}px;
    --title-color: {{ section.settings.title_color }};
    --title-size: {{ section.settings.title_size }}px;
    --title-size-mobile: {{ section.settings.title_size_mobile }}px;
    --description-color: {{ section.settings.description_color }};
    --description-size: {{ section.settings.description_size }}px;
    --description-size-mobile: {{ section.settings.description_size_mobile }}px;
    --button-bg: {{ section.settings.button_background_color }};
    --button-color: {{ section.settings.button_text_color }};
    --button-hover-bg: {{ section.settings.button_hover_background_color }};
    --button-hover-color: {{ section.settings.button_hover_text_color }};
    --guarantee-color: {{ section.settings.guarantee_text_color }};
    --content-gap: {{ section.settings.content_gap }}px;
    --content-gap-mobile: {{ section.settings.content_gap_mobile }}px;
  "
>
  <div class="risk-free-trial-container">
    <div class="risk-free-trial-content">
      <div class="risk-free-trial-image">
        {%- if section.settings.image != blank -%}
          <img
            src="{{ section.settings.image | image_url: width: section.settings.image_width }}"
            alt="{{ section.settings.image.alt | default: section.settings.title | escape }}"
            width="{{ section.settings.image_width }}"
            height="{{ section.settings.image_height }}"
            loading="{% if lazy_load %}lazy{% else %}eager{% endif %}"
            class="risk-free-trial-img"
          >
        {%- else -%}
          <div class="risk-free-trial-image-placeholder">
            <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="10" y="20" width="80" height="60" rx="4" stroke="#ccc" stroke-width="2" fill="#f8f9fa"/>
              <circle cx="30" cy="35" r="8" stroke="#ccc" stroke-width="2" fill="none"/>
              <path d="M20 65 L35 50 L50 65 L65 45 L80 60" stroke="#ccc" stroke-width="2" fill="none"/>
            </svg>
            <p>Add an image to showcase your product</p>
          </div>
        {%- endif -%}
      </div>
      
      <div class="risk-free-trial-text">
        {%- if section.settings.title != blank -%}
          <{{ heading_tag }} class="risk-free-trial-title">
            {{ section.settings.title | escape }}
          </{{ heading_tag }}>
        {%- endif -%}
        
        {%- if section.settings.description != blank -%}
          <div class="risk-free-trial-description">
            {{ section.settings.description | newline_to_br }}
          </div>
        {%- endif -%}
        
        <div class="risk-free-trial-button-container">
          {%- if section.settings.button_text != blank -%}
            <a 
              href="{{ section.settings.button_url | default: '#' }}" 
              class="risk-free-trial-btn"
              {% if section.settings.button_url == blank %}aria-disabled="true"{% endif %}
              {% if section.settings.button_url contains 'http' %}target="_blank" rel="noopener noreferrer"{% endif %}
            >
              {{ section.settings.button_text | escape }}
            </a>
          {%- endif -%}
          
          {%- if section.settings.guarantee_text != blank -%}
            <p class="risk-free-trial-guarantee">
              {{ section.settings.guarantee_text | escape }}
            </p>
          {%- endif -%}
        </div>
      </div>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Risk-Free Trial",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Try your Upsteps for 180 days, Risk-Free!",
      "info": "Main heading for the section"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading tag",
      "options": [
        {
          "value": "h1",
          "label": "H1"
        },
        {
          "value": "h2",
          "label": "H2"
        },
        {
          "value": "h3",
          "label": "H3"
        },
        {
          "value": "h4",
          "label": "H4"
        }
      ],
      "default": "h2",
      "info": "Choose the appropriate heading level for SEO"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>We 100% guarantee that you'll benefit from our custom orthotics. If they don't feel right, we'll remake them until they fit perfectly.</p><p>If you're not in love with them within 180 days, we'll refund your money - no questions asked!</p>",
      "info": "Supports basic HTML formatting"
    },
    {
      "type": "header",
      "content": "Call to Action"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "TAKE THE QUIZ",
      "info": "Leave empty to hide button"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button URL",
      "info": "Link destination for the button"
    },
    {
      "type": "text",
      "id": "guarantee_text",
      "label": "Guarantee text",
      "default": "180-day money-back guarantee",
      "info": "Text displayed below the button"
    },
    {
      "type": "header",
      "content": "Image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "Displayed on the left side of the section"
    },
    {
      "type": "range",
      "id": "image_width",
      "label": "Image width",
      "min": 200,
      "max": 800,
      "step": 50,
      "default": 700,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "image_height",
      "label": "Image height",
      "min": 200,
      "max": 800,
      "step": 50,
      "default": 600,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#4a4a4a"
    },
    {
      "type": "range",
      "id": "title_size",
      "label": "Title size",
      "min": 20,
      "max": 60,
      "step": 2,
      "default": 36,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "title_size_mobile",
      "label": "Title size (mobile)",
      "min": 18,
      "max": 40,
      "step": 2,
      "default": 28,
      "unit": "px"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#4a4a4a"
    },
    {
      "type": "range",
      "id": "description_size",
      "label": "Description size",
      "min": 12,
      "max": 24,
      "step": 1,
      "default": 16,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "description_size_mobile",
      "label": "Description size (mobile)",
      "min": 12,
      "max": 20,
      "step": 1,
      "default": 14,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Button Styling"
    },
    {
      "type": "color",
      "id": "button_background_color",
      "label": "Button background color",
      "default": "#17a2b8"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_hover_background_color",
      "label": "Button hover background color",
      "default": "#138496"
    },
    {
      "type": "color",
      "id": "button_hover_text_color",
      "label": "Button hover text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "guarantee_text_color",
      "label": "Guarantee text color",
      "default": "#6c757d"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "content_gap",
      "label": "Content gap",
      "min": 20,
      "max": 100,
      "step": 10,
      "default": 60,
      "unit": "px",
      "info": "Space between image and text on desktop"
    },
    {
      "type": "range",
      "id": "content_gap_mobile",
      "label": "Content gap (mobile)",
      "min": 20,
      "max": 80,
      "step": 10,
      "default": 40,
      "unit": "px",
      "info": "Space between image and text on mobile"
    },
    {
      "type": "range",
      "id": "section_padding_top",
      "label": "Section padding top",
      "min": 0,
      "max": 120,
      "step": 10,
      "default": 60,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "section_padding_bottom",
      "label": "Section padding bottom",
      "min": 0,
      "max": 120,
      "step": 10,
      "default": 60,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "section_padding_top_mobile",
      "label": "Section padding top (mobile)",
      "min": 0,
      "max": 80,
      "step": 10,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "section_padding_bottom_mobile",
      "label": "Section padding bottom (mobile)",
      "min": 0,
      "max": 80,
      "step": 10,
      "default": 40,
      "unit": "px"
    }
  ],
  "presets": [
    {
      "name": "Risk-Free Trial",
      "settings": {
        "title": "Try your Upsteps for 180 days, Risk-Free!",
        "description": "<p>We 100% guarantee that you'll benefit from our custom orthotics. If they don't feel right, we'll remake them until they fit perfectly.</p><p>If you're not in love with them within 180 days, we'll refund your money - no questions asked!</p>",
        "button_text": "TAKE THE QUIZ",
        "guarantee_text": "180-day money-back guarantee",
        "image_width": 700,
        "image_height": 600
      }
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
