{{ 'section-comparison.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign section_id = 'comparison-' | append: section.id
  assign heading_tag = section.settings.heading_tag | default: 'h2'
  assign lazy_load = true
  if section.index <= 2
    assign lazy_load = false
  endif
-%}

<section
  id="{{ section_id }}"
  class="comparison-section"
  data-section-id="{{ section.id }}"
  data-section-type="comparison"
>
  <div class="comparison-container">
    <div class="comparison-content">
      <div class="comparison-text">
        {%- if section.settings.title != blank -%}
          <{{ heading_tag }} class="comparison-title">
            {{ section.settings.title | escape }}
          </{{ heading_tag }}>
        {%- endif -%}

        {%- if section.settings.description != blank -%}
          <div class="comparison-description">
            {{ section.settings.description | newline_to_br }}
          </div>
        {%- endif -%}

        <div class="comparison-button-container">
          {%- if section.settings.button_text != blank -%}
            <a
              href="{{ section.settings.button_url | default: '#' }}"
              class="comparison-btn"
              {% if section.settings.button_url == blank %}aria-disabled="true"{% endif %}
              {% if section.settings.button_url contains 'http' %}target="_blank" rel="noopener noreferrer"{% endif %}
            >
              {{ section.settings.button_text | escape }}
            </a>
          {%- endif -%}

          {%- if section.settings.guarantee_text != blank -%}
            <p class="comparison-guarantee">{{ section.settings.guarantee_text | escape }}</p>
          {%- endif -%}
        </div>
      </div>

      <div class="comparison-image">
        {%- if section.settings.image != blank -%}
          {%- liquid
            assign image_alt = section.settings.image.alt | default: section.settings.title | escape
            assign image_width = section.settings.image.width | default: 1420
            assign image_height = section.settings.image.height | default: 600
          -%}
          <img
            src="{{ section.settings.image | image_url: width: 600 }}"
            srcset="
              {{ section.settings.image | image_url: width: 300 }} 300w,
              {{ section.settings.image | image_url: width: 600 }} 600w,
              {{ section.settings.image | image_url: width: 900 }} 900w,
              {{ section.settings.image | image_url: width: 1200 }} 1200w,
              {{ section.settings.image | image_url: width: 1600 }} 1600w
            "
            sizes="(max-width: 480px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 45vw, 50vw"
            alt="{{ image_alt }}"
            {% if lazy_load %}loading="lazy"{% else %}loading="eager"{% endif %}
            width="{{ image_width }}"
            height="{{ image_height }}"
            decoding="async"
            fetchpriority="{% if lazy_load %}auto{% else %}high{% endif %}"
          >
        {%- else -%}
          <div class="comparison-image-placeholder" role="img" aria-label="Placeholder for comparison image">
            <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <rect width="100" height="100" fill="#E5E5E5"/>
              <path d="M30 40L50 60L70 40" stroke="#999" stroke-width="2" fill="none"/>
              <circle cx="40" cy="30" r="5" fill="#999"/>
            </svg>
            <p>Comparison Image</p>
          </div>
        {%- endif -%}
      </div>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Comparison Section",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Get more, pay less - a premium solution at no extra charge.",
      "info": "Main heading for the comparison section"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading tag",
      "options": [
        {
          "value": "h1",
          "label": "H1"
        },
        {
          "value": "h2",
          "label": "H2"
        },
        {
          "value": "h3",
          "label": "H3"
        },
        {
          "value": "h4",
          "label": "H4"
        }
      ],
      "default": "h2",
      "info": "Choose the appropriate heading level for SEO"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Your Upsteps will be customized specifically to address your foot pain needs. They're designed for both daily and athletic activities, cost 60% less than any other custom orthotic brand, plus, are completely risk-free to try for 180 days!</p>",
      "info": "Supports basic HTML formatting"
    },
    {
      "type": "header",
      "content": "Call to Action"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "TAKE THE QUIZ",
      "info": "Leave empty to hide button"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button URL",
      "info": "Link destination for the button"
    },
    {
      "type": "text",
      "id": "guarantee_text",
      "label": "Guarantee text",
      "default": "180-day money-back guarantee",
      "info": "Text displayed below the button"
    },
    {
      "type": "header",
      "content": "Image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Comparison image",
      "info": "Recommended size: 1420x600px for best quality"
    }
  ],
  "presets": [
    {
      "name": "Comparison Section",
      "settings": {
        "title": "Get more, pay less - a premium solution at no extra charge.",
        "description": "<p>Your Upsteps will be customized specifically to address your foot pain needs. They're designed for both daily and athletic activities, cost 60% less than any other custom orthotic brand, plus, are completely risk-free to try for 180 days!</p>",
        "button_text": "TAKE THE QUIZ",
        "guarantee_text": "180-day money-back guarantee"
      }
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
