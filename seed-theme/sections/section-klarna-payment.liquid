<section class="klarna-payment-section">
  <div class="klarna-container">
    <div class="klarna-content">
      {%- if section.settings.title != blank -%}
        <h2 class="klarna-title">{{ section.settings.title }}</h2>
      {%- endif -%}
      
      {%- if section.settings.subtitle != blank -%}
        <p class="klarna-subtitle">{{ section.settings.subtitle }}</p>
      {%- endif -%}
      
      <div class="klarna-bottom-row">
        {%- if section.settings.logo_svg != blank -%}
          <div class="klarna-logo">
            {{ section.settings.logo_svg }}
          </div>
        {%- else -%}
          <div class="klarna-logo-text">
            KLARNA.
          </div>
        {%- endif -%}

        {%- if section.settings.show_learn_more and section.settings.learn_more_text != blank -%}
          <button class="klarna-learn-more" onclick="openKlarnaPopup()">
            {{ section.settings.learn_more_text }}
          </button>
        {%- endif -%}
      </div>
    </div>
  </div>
</section>

<!-- Klarna Popup -->
{%- if section.settings.show_learn_more -%}
  <div id="klarna-popup" class="klarna-popup-overlay" onclick="closeKlarnaPopup()">
    <div class="klarna-popup" onclick="event.stopPropagation()">
      <div class="klarna-popup-header">
        {%- if section.settings.popup_logo_svg != blank -%}
          <div class="klarna-popup-logo">
            {{ section.settings.popup_logo_svg }}
          </div>
        {%- else -%}
          <div class="klarna-popup-logo-text">
            Klarna.
          </div>
        {%- endif -%}
        <button class="klarna-popup-close" onclick="closeKlarnaPopup()">×</button>
      </div>
      
      <div class="klarna-popup-content">
        {%- if section.settings.popup_title != blank -%}
          <h3 class="klarna-popup-title">{{ section.settings.popup_title }}</h3>
        {%- endif -%}
        
        {%- if section.settings.popup_description != blank -%}
          <p class="klarna-popup-description">{{ section.settings.popup_description }}</p>
        {%- endif -%}
        
        <div class="klarna-steps">
          <div class="klarna-step">
            <div class="klarna-step-icon"></div>
            <div class="klarna-step-text">{{ section.settings.step_1_text | default: "Add item(s) to your cart" }}</div>
          </div>

          <div class="klarna-step">
            <div class="klarna-step-icon"></div>
            <div class="klarna-step-text">
              {{ section.settings.step_2_text | default: "Go to checkout and choose" }}
              <span class="klarna-highlight">Klarna</span>
            </div>
          </div>

          <div class="klarna-step">
            <div class="klarna-step-icon"></div>
            <div class="klarna-step-text">{{ section.settings.step_3_text | default: "Enter your debit or credit card information" }}</div>
          </div>

          <div class="klarna-step">
            <div class="klarna-step-icon"></div>
            <div class="klarna-step-text">{{ section.settings.step_4_text | default: "Your first payment is taken when the order is processed and the remaining 3 are automatically taken every two weeks" }}</div>
          </div>
        </div>
        
        {%- if section.settings.popup_footer_text != blank -%}
          <p class="klarna-popup-footer">{{ section.settings.popup_footer_text }}</p>
        {%- endif -%}
        
        <button class="klarna-popup-close-btn" onclick="closeKlarnaPopup()">
          {{ section.settings.close_button_text | default: "Close" }}
        </button>
      </div>
    </div>
  </div>
{%- endif -%}

<style>
  .shopify-section-klarna-payment .klarna-payment-section {
    background: transparent;
    padding: 5px 0px;
    box-sizing: border-box;
  }

  .shopify-section-klarna-payment .klarna-container {
    max-width: 1098px;
    height: 149px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 14px;
    background: #BEEBFC;
  }

  .shopify-section-klarna-payment .klarna-content {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .shopify-section-klarna-payment .klarna-title {
    color: #002366;
    text-align: center;
    font-family: Lato_, sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 900;
    line-height: 24px;
    letter-spacing: 0.64px;
    margin: 0;
    padding: 0;
  }

  .shopify-section-klarna-payment .klarna-subtitle {
    color: #002366;
    text-align: center;
    font-family: Lato_, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.533px;
    margin: 0;
    padding: 0;
  }

  .shopify-section-klarna-payment .klarna-bottom-row {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .shopify-section-klarna-payment .klarna-logo {
    margin: 8px 0;
  }

  .shopify-section-klarna-payment .klarna-logo svg {
    height: 24px;
    width: auto;
  }

  .shopify-section-klarna-payment .klarna-logo-text {
    font-family: Lato_, sans-serif;
    font-size: 24px;
    font-weight: bold;
    color: #002366;
    letter-spacing: 1px;
    margin: 0;
  }

  .shopify-section-klarna-payment .klarna-learn-more {
    color: #002366 !important;
    text-align: left;
    font-family: Lato_, sans-serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.433px;
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
    background: none !important;
    border: none !important;
    cursor: pointer;
    padding: 0 !important;
    margin: 0 !important;
    position: relative;
    box-shadow: none !important;
    animation: none !important;
    transition: opacity 0.2s ease !important;
    max-width: 70px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .shopify-section-klarna-payment .klarna-learn-more:before,
  .shopify-section-klarna-payment .klarna-learn-more:after {
    display: none !important;
    content: none !important;
  }

  .shopify-section-klarna-payment .klarna-learn-more:hover {
    opacity: 0.7 !important;
    animation: none !important;
    background: none !important;
    box-shadow: none !important;
    color: #002366 !important;
  }

  /* Popup Styles */
  .shopify-section-klarna-payment .klarna-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .shopify-section-klarna-payment .klarna-popup-overlay.active {
    display: flex;
  }

  .shopify-section-klarna-payment .klarna-popup {
    background: white;
    border-radius: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }

  .shopify-section-klarna-payment .klarna-popup-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #eee;
    position: relative;
  }

  .shopify-section-klarna-payment .klarna-popup-logo svg {
    height: 30px;
    width: auto;
  }

  .shopify-section-klarna-payment .klarna-popup-logo-text {
    font-family: Lato_, sans-serif;
    font-size: 24px;
    font-weight: bold;
    color: #999;
    letter-spacing: 1px;
    margin: 0;
  }

  .shopify-section-klarna-payment .klarna-popup-close {
    background: none !important;
    border: none !important;
    font-size: 20px;
    cursor: pointer;
    color: #000 !important;
    padding: 5px !important;
    width: auto;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 15px;
    top: 25px;
    transform: none;
    box-shadow: none !important;
    animation: none !important;
    transition: opacity 0.2s ease !important;
    line-height: 1;
    min-width: 20px;
    min-height: 20px;
  }

  .shopify-section-klarna-payment .klarna-popup-close:before,
  .shopify-section-klarna-payment .klarna-popup-close:after {
    display: none !important;
    content: none !important;
  }

  .shopify-section-klarna-payment .klarna-popup-close:hover {
    color: #000 !important;
    opacity: 0.7 !important;
    background: none !important;
    box-shadow: none !important;
    animation: none !important;
  }

  .shopify-section-klarna-payment .klarna-popup-content {
    padding: 30px;
  }

  .shopify-section-klarna-payment .klarna-popup-title {
    font-family: Lato_, sans-serif;
    font-size: 24px;
    font-weight: 700;
    color: #000;
    margin: 0 0 16px 0;
    text-align: left;
  }

  .shopify-section-klarna-payment .klarna-popup-description {
    font-family: Lato_, sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: #666;
    margin: 0 0 30px 0;
    line-height: 1.5;
  }

  .shopify-section-klarna-payment .klarna-steps {
    margin: 30px 0;
  }

  .shopify-section-klarna-payment .klarna-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 12px;
  }

  .shopify-section-klarna-payment .klarna-step:last-child {
    margin-bottom: 0;
  }

  .shopify-section-klarna-payment .klarna-step-icon {
    width: 4px;
    height: 4px;
    background: #000;
    border-radius: 0;
    margin-top: 10px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .shopify-section-klarna-payment .klarna-step-text {
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: #0E0E0F;
    line-height: 1.5;
    margin: 0;
    padding: 0;
  }

  .shopify-section-klarna-payment .klarna-highlight {
    background: #FFB3C7;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
  }

  .shopify-section-klarna-payment .klarna-popup-footer {
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 12px;
    font-weight: 400;
    color: #5F6163;
    margin: 30px 0 0 0;
    line-height: 1.4;
    background: none;
    padding: 0;
    border-radius: 0;
  }

  .shopify-section-klarna-payment .klarna-popup-close-btn {
    background: #000000 !important;
    color: #FFFFFF !important;
    border: none !important;
    padding: 12px 40px !important;
    border-radius: 6px;
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
    margin-top: 30px;
    box-shadow: none !important;
    animation: none !important;
    transition: opacity 0.2s ease !important;
    position: relative;
  }

  .shopify-section-klarna-payment .klarna-popup-close-btn:before,
  .shopify-section-klarna-payment .klarna-popup-close-btn:after {
    display: none !important;
    content: none !important;
  }

  .shopify-section-klarna-payment .klarna-popup-close-btn:hover {
    background: #000000 !important;
    color: #FFFFFF !important;
    opacity: 0.8 !important;
    animation: none !important;
    box-shadow: none !important;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .shopify-section-klarna-payment .klarna-container {
      max-width: 100%;
      height: auto;
      min-height: 120px;
      padding: 20px 16px;
    }

    .shopify-section-klarna-payment .klarna-title {
      font-size: 20px;
      line-height: 22px;
      letter-spacing: 0.5px;
    }

    .shopify-section-klarna-payment .klarna-subtitle {
      font-size: 14px;
      letter-spacing: 0.4px;
    }

    .shopify-section-klarna-payment .klarna-learn-more {
      font-size: 12px;
      letter-spacing: 0.3px;
    }

    .shopify-section-klarna-payment .klarna-popup {
      width: 95%;
      margin: 20px;
    }

    .shopify-section-klarna-payment .klarna-popup-header {
      padding: 15px 20px;
    }

    .shopify-section-klarna-payment .klarna-popup-content {
      padding: 20px;
    }

    .shopify-section-klarna-payment .klarna-popup-title {
      font-size: 20px;
    }

    .shopify-section-klarna-payment .klarna-popup-description {
      font-size: 14px;
    }

    .shopify-section-klarna-payment .klarna-step-text {
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    .shopify-section-klarna-payment .klarna-payment-section {
      padding: 30px 0;
    }

    .shopify-section-klarna-payment .klarna-container {
      min-height: 100px;
      padding: 16px 12px;
    }

    .shopify-section-klarna-payment .klarna-title {
      font-size: 18px;
      line-height: 20px;
    }

    .shopify-section-klarna-payment .klarna-subtitle {
      font-size: 13px;
    }

    .shopify-section-klarna-payment .klarna-popup-header {
      padding: 12px 16px;
    }

    .shopify-section-klarna-payment .klarna-popup-content {
      padding: 16px;
    }

    .shopify-section-klarna-payment .klarna-popup-title {
      font-size: 18px;
    }
  }
</style>

<script>
  function openKlarnaPopup() {
    const popup = document.getElementById('klarna-popup');
    if (popup) {
      popup.classList.add('active');
      document.body.style.overflow = 'hidden';
      document.body.classList.add('klarna-popup-open');
    }
  }

  function closeKlarnaPopup() {
    const popup = document.getElementById('klarna-popup');
    if (popup) {
      popup.classList.remove('active');
      document.body.style.overflow = '';
      document.body.classList.remove('klarna-popup-open');
    }
  }

  // Close popup on Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeKlarnaPopup();
    }
  });
</script>

{% schema %}
{
  "name": "Klarna Payment Section",
  "class": "shopify-section-klarna-payment",
  "settings": [
    {
      "type": "header",
      "content": "Main Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Shop now. Pay in 4 interest-free payments"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "As low as $62.25/mo for Upsteps"
    },
    {
      "type": "textarea",
      "id": "logo_svg",
      "label": "Custom Logo SVG",
      "info": "Paste your custom SVG code here. Leave empty to use default Klarna logo."
    },
    {
      "type": "header",
      "content": "Learn More Button"
    },
    {
      "type": "checkbox",
      "id": "show_learn_more",
      "label": "Show Learn More Button",
      "default": true
    },
    {
      "type": "text",
      "id": "learn_more_text",
      "label": "Learn More Button Text",
      "default": "Learn more"
    },
    {
      "type": "header",
      "content": "Popup Settings"
    },
    {
      "type": "textarea",
      "id": "popup_logo_svg",
      "label": "Popup Logo SVG",
      "info": "Custom SVG for popup header. Leave empty to use default."
    },
    {
      "type": "text",
      "id": "popup_title",
      "label": "Popup Title",
      "default": "4 interest-free payments of $62.25"
    },
    {
      "type": "textarea",
      "id": "popup_description",
      "label": "Popup Description",
      "default": "Buy what you love and split the cost. It's easy and interest-free."
    },
    {
      "type": "header",
      "content": "Popup Steps"
    },
    {
      "type": "text",
      "id": "step_1_text",
      "label": "Step 1 Text",
      "default": "Add item(s) to your cart"
    },
    {
      "type": "text",
      "id": "step_2_text",
      "label": "Step 2 Text",
      "default": "Go to checkout and choose"
    },
    {
      "type": "text",
      "id": "step_3_text",
      "label": "Step 3 Text",
      "default": "Enter your debit or credit card information"
    },
    {
      "type": "textarea",
      "id": "step_4_text",
      "label": "Step 4 Text",
      "default": "Your first payment is taken when the order is processed and the remaining 3 are automatically taken every two weeks"
    },
    {
      "type": "textarea",
      "id": "popup_footer_text",
      "label": "Popup Footer Text",
      "default": "See payment terms. A higher initial payment may be required for some consumers. CA residents: Loans made or arranged pursuant to a California Financing Law license."
    },
    {
      "type": "text",
      "id": "close_button_text",
      "label": "Close Button Text",
      "default": "Close"
    }
  ],
  "presets": [
    {
      "name": "Klarna Payment Section",
      "settings": {
        "title": "Shop now. Pay in 4 interest-free payments",
        "subtitle": "As low as $62.25/mo for Upsteps",
        "show_learn_more": true,
        "learn_more_text": "Learn more",
        "popup_title": "4 interest-free payments of $62.25",
        "popup_description": "Buy what you love and split the cost. It's easy and interest-free.",
        "step_1_text": "Add item(s) to your cart",
        "step_2_text": "Go to checkout and choose",
        "step_3_text": "Enter your debit or credit card information",
        "step_4_text": "Your first payment is taken when the order is processed and the remaining 3 are automatically taken every two weeks",
        "popup_footer_text": "See payment terms. A higher initial payment may be required for some consumers. CA residents: Loans made or arranged pursuant to a California Financing Law license.",
        "close_button_text": "Close"
      }
    }
  ]
}
{% endschema %}
