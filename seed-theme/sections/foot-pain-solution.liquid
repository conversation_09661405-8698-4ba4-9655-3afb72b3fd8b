{{ 'section-foot-pain-solution.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign section_id = 'foot-pain-' | append: section.id
  assign heading_tag = section.settings.heading_tag | default: 'h2'
  assign lazy_load = true
  if section.index <= 2
    assign lazy_load = false
  endif
-%}

<section 
  id="{{ section_id }}"
  class="foot-pain-section"
  data-section-id="{{ section.id }}"
  data-section-type="foot-pain-solution"
>
  <div class="foot-pain-container">
    <div class="foot-pain-content">
      <div class="foot-pain-image">
        {%- if section.settings.image != blank -%}
          {%- liquid
            assign image_alt = section.settings.image.alt | default: section.settings.title | escape
            assign image_width = section.settings.image.width | default: 620
            assign image_height = section.settings.image.height | default: 379
          -%}
          <img 
            src="{{ section.settings.image | image_url: width: 620 }}"
            srcset="
              {{ section.settings.image | image_url: width: 310 }} 310w,
              {{ section.settings.image | image_url: width: 620 }} 620w,
              {{ section.settings.image | image_url: width: 930 }} 930w,
              {{ section.settings.image | image_url: width: 1240 }} 1240w
            "
            sizes="(max-width: 480px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 45vw, 50vw"
            alt="{{ image_alt }}"
            {% if lazy_load %}loading="lazy"{% else %}loading="eager"{% endif %}
            width="{{ image_width }}"
            height="{{ image_height }}"
            decoding="async"
            fetchpriority="{% if lazy_load %}auto{% else %}high{% endif %}"
          >
        {%- else -%}
          <div class="foot-pain-image-placeholder" role="img" aria-label="Placeholder for foot pain solution image">
            <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <rect width="100" height="100" fill="#E5E5E5"/>
              <path d="M30 40L50 60L70 40" stroke="#999" stroke-width="2" fill="none"/>
              <circle cx="40" cy="30" r="5" fill="#999"/>
            </svg>
            <p>Foot Pain Solution Image</p>
          </div>
        {%- endif -%}
      </div>
      
      <div class="foot-pain-text">
        {%- if section.settings.title != blank -%}
          <{{ heading_tag }} class="foot-pain-title">
            {{ section.settings.title | escape }}
          </{{ heading_tag }}>
        {%- endif -%}
        
        {%- if section.settings.description != blank -%}
          <div class="foot-pain-description">
            {{ section.settings.description | newline_to_br }}
          </div>
        {%- endif -%}
        
        {%- if section.blocks.size > 0 -%}
          <ul class="foot-pain-features">
            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when 'feature' -%}
                  {%- if block.settings.text != blank -%}
                    <li class="foot-pain-feature-item" {{ block.shopify_attributes }}>
                      {{ block.settings.text | escape }}
                    </li>
                  {%- endif -%}
              {%- endcase -%}
            {%- endfor -%}
          </ul>
        {%- endif -%}
        
        <div class="foot-pain-button-container">
          {%- if section.settings.button_text != blank -%}
            <a 
              href="{{ section.settings.button_url | default: '#' }}" 
              class="foot-pain-btn"
              {% if section.settings.button_url == blank %}aria-disabled="true"{% endif %}
              {% if section.settings.button_url contains 'http' %}target="_blank" rel="noopener noreferrer"{% endif %}
            >
              {{ section.settings.button_text | escape }}
            </a>
          {%- endif -%}
          
          {%- if section.settings.guarantee_text != blank -%}
            <p class="foot-pain-guarantee">{{ section.settings.guarantee_text | escape }}</p>
          {%- endif -%}
        </div>
      </div>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Foot Pain Solution",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Goodbye, foot pain.",
      "info": "Main heading for the foot pain solution section"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading tag",
      "options": [
        {
          "value": "h1",
          "label": "H1"
        },
        {
          "value": "h2",
          "label": "H2"
        },
        {
          "value": "h3",
          "label": "H3"
        },
        {
          "value": "h4",
          "label": "H4"
        }
      ],
      "default": "h2",
      "info": "Choose the appropriate heading level for SEO"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Reduce foot fatigue and discomfort when you're standing or walking for more than 4 hours a day. The perfect blend of support and cushioning in one orthotic design.</p>",
      "info": "Supports basic HTML formatting"
    },
    {
      "type": "header",
      "content": "Call to Action"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "TAKE THE QUIZ",
      "info": "Leave empty to hide button"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button URL",
      "info": "Link destination for the button"
    },
    {
      "type": "text",
      "id": "guarantee_text",
      "label": "Guarantee text",
      "default": "180-day money-back guarantee",
      "info": "Text displayed below the button"
    },
    {
      "type": "header",
      "content": "Image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Solution image",
      "info": "Recommended size: 620x379px for best quality"
    }
  ],
  "blocks": [
    {
      "type": "feature",
      "name": "Feature",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Feature text",
          "default": "Maximum support for the arches of the feet"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Foot Pain Solution",
      "settings": {
        "title": "Goodbye, foot pain.",
        "description": "<p>Reduce foot fatigue and discomfort when you're standing or walking for more than 4 hours a day. The perfect blend of support and cushioning in one orthotic design.</p>",
        "button_text": "TAKE THE QUIZ",
        "guarantee_text": "180-day money-back guarantee"
      },
      "blocks": [
        {
          "type": "feature",
          "settings": {
            "text": "Maximum support for the arches of the feet"
          }
        },
        {
          "type": "feature",
          "settings": {
            "text": "A durable Polypropylene shell"
          }
        },
        {
          "type": "feature",
          "settings": {
            "text": "High-quality top covers and cushioning mid-layers"
          }
        },
        {
          "type": "feature",
          "settings": {
            "text": "Multiple design options to allow them to fit any shoe"
          }
        },
        {
          "type": "feature",
          "settings": {
            "text": "Specific features to alleviate common foot pains"
          }
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
