{{ 'section-three-step-plan.css' | asset_url | stylesheet_tag }}

<div class="three-step-plan-section">
  <div class="page-width">
    {%- if section.settings.title != blank -%}
      <h2 class="three-step-plan-title">{{ section.settings.title }}</h2>
    {%- endif -%}
    
    <div class="three-step-plan-cards">
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when 'step_card' -%}
            <div class="step-card" {{ block.shopify_attributes }}>
              <div class="step-card-image">
                {%- if block.settings.image != blank -%}
                  <img src="{{ block.settings.image | image_url: width: 320 }}"
                       alt="{{ block.settings.step_title | escape }}"
                       width="320"
                       height="200"
                       loading="lazy">
                {%- else -%}
                  <div class="step-card-placeholder"></div>
                {%- endif -%}
              </div>
              
              <div class="step-card-content">
                {%- if block.settings.step_title != blank -%}
                  <h3 class="step-card-title">{{ block.settings.step_title }}</h3>
                {%- endif -%}
                
                {%- if block.settings.step_description != blank -%}
                  <p class="step-card-description">{{ block.settings.step_description }}</p>
                {%- endif -%}
              </div>
            </div>
        {%- endcase -%}
      {%- endfor -%}
    </div>
    
    {%- if section.settings.primary_button_text != blank or section.settings.secondary_button_text != blank -%}
      <div class="three-step-plan-buttons">
        {%- if section.settings.primary_button_text != blank -%}
          <a href="{{ section.settings.primary_button_url | default: '#' }}" 
             class="three-step-plan-btn three-step-plan-btn-primary">
            {{ section.settings.primary_button_text }}
          </a>
        {%- endif -%}
        
        {%- if section.settings.secondary_button_text != blank -%}
          <button type="button"
                  class="three-step-plan-video-btn{% unless section.settings.video_url != blank or section.settings.shopify_video != blank %} disabled{% endunless %}"
                  onclick="openVideoModal()"
                  {% unless section.settings.video_url != blank or section.settings.shopify_video != blank %}disabled{% endunless %}>
            {{ section.settings.secondary_button_text }}
          </button>
        {%- endif -%}
      </div>
    {%- endif -%}
  </div>
</div>

<!-- Video Modal -->
{%- if section.settings.video_url != blank or section.settings.shopify_video != blank -%}
  <div id="video-modal" class="video-modal" onclick="closeVideoModal()" style="display:none">
    <button class="video-close-btn" onclick="closeVideoModal()" aria-label="Close video">&times;</button>
    <div class="video-container" onclick="event.stopPropagation()">
      {%- if section.settings.shopify_video != blank -%}
        {{ section.settings.shopify_video | video_tag:
            image_size: '1920x1080',
            controls: true,
            muted: false,
            id: 'modal-video',
            preload: 'none',
            loading: 'lazy'
        }}
      {%- elsif section.settings.video_url != blank -%}
        {%- liquid
            assign video_id = ''
            assign video_type = ''

            if section.settings.video_url contains 'youtube.com/watch?v='
                assign video_id = section.settings.video_url | split: 'v=' | last | split: '&' | first
                assign video_type = 'youtube'
            elsif section.settings.video_url contains 'youtu.be/'
                assign video_id = section.settings.video_url | split: 'youtu.be/' | last | split: '?' | first
                assign video_type = 'youtube'
            elsif section.settings.video_url contains 'vimeo.com/'
                assign video_id = section.settings.video_url | split: 'vimeo.com/' | last | split: '?' | first
                assign video_type = 'vimeo'
            endif
        -%}

        {%- if video_type == 'youtube' and video_id != blank -%}
          <iframe id="modal-iframe"
                  data-src="https://www.youtube.com/embed/{{ video_id }}?autoplay=1&rel=0&modestbranding=1&controls=1"
                  loading="lazy"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowfullscreen>
          </iframe>
        {%- elsif video_type == 'vimeo' and video_id != blank -%}
          <iframe id="modal-iframe"
                  data-src="https://player.vimeo.com/video/{{ video_id }}?autoplay=1&controls=1"
                  loading="lazy"
                  allow="autoplay; fullscreen; picture-in-picture"
                  allowfullscreen>
          </iframe>
        {%- endif -%}
      {%- endif -%}
    </div>
  </div>
{%- endif -%}

<script>
// Optimized video modal with cached elements
(function() {
  let modal, iframe, video, isInitialized = false;

  function initElements() {
    if (isInitialized) return;
    modal = document.getElementById('video-modal');
    if (modal) {
      iframe = modal.querySelector('#modal-iframe');
      video = modal.querySelector('#modal-video');
      isInitialized = true;
    }
  }

  window.openVideoModal = function() {
    initElements();
    if (!modal) return;

    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // Start video playback
    if (iframe?.dataset.src) {
      iframe.src = iframe.dataset.src;
    }

    if (video) {
      video.currentTime = 0;
      video.play().catch(() => {});
    }
  };

  window.closeVideoModal = function() {
    if (!modal) return;

    modal.style.display = 'none';
    document.body.style.overflow = '';

    // Stop video playback
    if (iframe) iframe.src = '';
    if (video) {
      video.pause();
      video.currentTime = 0;
    }
  };

  // Close modal on Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') window.closeVideoModal();
  }, { passive: true });
})();
</script>

{% schema %}
{
  "name": "Three Step Plan",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Your 3-step plan to achieve foot-pain relief"
    },
    {
      "type": "header",
      "content": "Primary Button"
    },
    {
      "type": "text",
      "id": "primary_button_text",
      "label": "Primary Button Text",
      "default": "GET STARTED"
    },
    {
      "type": "url",
      "id": "primary_button_url",
      "label": "Primary Button URL"
    },
    {
      "type": "header",
      "content": "Secondary Button"
    },
    {
      "type": "text",
      "id": "secondary_button_text",
      "label": "Secondary Button Text",
      "default": "WATCH VIDEO"
    },
    {
      "type": "header",
      "content": "Video Modal Settings"
    },
    {
      "type": "text",
      "id": "video_title",
      "label": "Video Modal Title",
      "default": "How to Make your Footprint"
    },
    {
      "type": "video",
      "id": "shopify_video",
      "label": "Shopify Hosted Video",
      "info": "Upload a video file to Shopify (takes priority over external video)"
    },
    {
      "type": "url",
      "id": "video_url",
      "label": "External Video URL",
      "info": "YouTube or Vimeo URL (used if no Shopify video is uploaded)"
    }
  ],
  "blocks": [
    {
      "type": "step_card",
      "name": "Step Card",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Step Image"
        },
        {
          "type": "text",
          "id": "step_title",
          "label": "Step Title",
          "default": "Step 1"
        },
        {
          "type": "textarea",
          "id": "step_description",
          "label": "Step Description",
          "default": "Take 50 seconds to answer a few short questions - 100% risk-free with 180-day money-back."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Three Step Plan",
      "blocks": [
        {
          "type": "step_card",
          "settings": {
            "step_title": "Step 1",
            "step_description": "Take 50 seconds to answer a few short questions - 100% risk-free with 180-day money-back."
          }
        },
        {
          "type": "step_card",
          "settings": {
            "step_title": "Step 2",
            "step_description": "Get Upstep's impression kit at your door. Imprint your feet, and send it back free of charge."
          }
        },
        {
          "type": "step_card",
          "settings": {
            "step_title": "Step 3",
            "step_description": "We'll create and ship your Upsteps within 10-16 business days of receiving your impression kit."
          }
        }
      ]
    }
  ]
}
{% endschema %}
