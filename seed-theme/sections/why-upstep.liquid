{{ 'section-why-upstep.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign section_id = 'why-upstep-' | append: section.id
  assign heading_tag = section.settings.heading_tag | default: 'h2'
  assign lazy_load = true
  if section.index <= 2
    assign lazy_load = false
  endif
-%}

<section 
  id="{{ section_id }}"
  class="why-upstep-section"
  data-section-id="{{ section.id }}"
  data-section-type="why-upstep"
  style="
    --section-bg: {{ section.settings.background_color }};
    --section-padding-top: {{ section.settings.padding_top }}px;
    --section-padding-bottom: {{ section.settings.padding_bottom }}px;
    --section-padding-top-mobile: {{ section.settings.padding_top_mobile }}px;
    --section-padding-bottom-mobile: {{ section.settings.padding_bottom_mobile }}px;
    --title-color: {{ section.settings.title_color }};
    --title-size: {{ section.settings.title_size }}px;
    --title-size-mobile: {{ section.settings.title_size_mobile }}px;
    --description-color: {{ section.settings.description_color }};
    --description-size: {{ section.settings.description_size }}px;
    --description-size-mobile: {{ section.settings.description_size_mobile }}px;
    --button-bg: {{ section.settings.button_background_color }};
    --button-color: {{ section.settings.button_text_color }};
    --button-hover-bg: {{ section.settings.button_hover_background_color }};
    --button-hover-color: {{ section.settings.button_hover_text_color }};
    --guarantee-color: {{ section.settings.guarantee_text_color }};
    --content-gap: {{ section.settings.content_gap }}px;
    --content-gap-mobile: {{ section.settings.content_gap_mobile }}px;
  "
>
  <div class="why-upstep-container">
    <div class="why-upstep-content">
      <div class="why-upstep-text">
        {%- if section.settings.title != blank -%}
          <{{ heading_tag }} class="why-upstep-title">
            {{ section.settings.title | escape }}
          </{{ heading_tag }}>
        {%- endif -%}
        
        {%- if section.settings.description != blank -%}
          <div class="why-upstep-description">
            {{ section.settings.description | newline_to_br }}
          </div>
        {%- endif -%}
        
        <div class="why-upstep-button-container">
          {%- if section.settings.button_text != blank -%}
            <a 
              href="{{ section.settings.button_url | default: '#' }}" 
              class="why-upstep-btn"
              {% if section.settings.button_url == blank %}aria-disabled="true"{% endif %}
              {% if section.settings.button_url contains 'http' %}target="_blank" rel="noopener noreferrer"{% endif %}
            >
              {{ section.settings.button_text | escape }}
            </a>
          {%- endif -%}
          
          {%- if section.settings.guarantee_text != blank -%}
            <p class="why-upstep-guarantee">
              {{ section.settings.guarantee_text | escape }}
            </p>
          {%- endif -%}
        </div>
      </div>
      
      <div class="why-upstep-image">
        {%- if section.settings.svg_code != blank -%}
          <div class="why-upstep-svg-container">
            {{ section.settings.svg_code }}
          </div>
        {%- elsif section.settings.image != blank -%}
          <img 
            src="{{ section.settings.image | image_url: width: section.settings.image_width }}"
            alt="{{ section.settings.image.alt | default: section.settings.title | escape }}"
            width="{{ section.settings.image_width }}"
            height="{{ section.settings.image_height | default: section.settings.image_width }}"
            loading="{% if lazy_load %}lazy{% else %}eager{% endif %}"
            class="why-upstep-img"
          >
        {%- else -%}
          <div class="why-upstep-image-placeholder">
            <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="10" y="10" width="80" height="80" rx="8" fill="#f5f5f5" stroke="#e0e0e0" stroke-width="2"/>
              <path d="M30 40l10 10 20-20" stroke="#ccc" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="35" cy="35" r="3" fill="#ccc"/>
            </svg>
            <p>Image placeholder</p>
          </div>
        {%- endif -%}
      </div>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Why Upstep?",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Why Upstep?",
      "info": "Main heading for the section"
    },
    {
      "type": "select",
      "id": "heading_tag",
      "label": "Heading tag",
      "options": [
        {
          "value": "h1",
          "label": "H1"
        },
        {
          "value": "h2",
          "label": "H2"
        },
        {
          "value": "h3",
          "label": "H3"
        },
        {
          "value": "h4",
          "label": "H4"
        }
      ],
      "default": "h2",
      "info": "Choose the appropriate heading level for SEO"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>We will use top-quality materials for your needs and produce your custom orthotics with the most advanced manufacturing methods!</p><p>Then we will send your custom orthotics via FedEx, to your doorstep.</p>",
      "info": "Supports basic HTML formatting"
    },
    {
      "type": "header",
      "content": "Call to Action"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "TAKE THE QUIZ",
      "info": "Leave empty to hide button"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button URL",
      "info": "Link destination for the button"
    },
    {
      "type": "text",
      "id": "guarantee_text",
      "label": "Guarantee text",
      "default": "180-day money-back guarantee",
      "info": "Text displayed below the button"
    },
    {
      "type": "header",
      "content": "Image or SVG"
    },
    {
      "type": "html",
      "id": "svg_code",
      "label": "SVG Code",
      "info": "Paste SVG code here. If both SVG and image are provided, SVG will be displayed first."
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "Displayed on the right side of the section. Will be used if no SVG is provided."
    },
    {
      "type": "range",
      "id": "image_width",
      "label": "Image width",
      "min": 200,
      "max": 800,
      "step": 50,
      "default": 500,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "image_height",
      "label": "Image height",
      "min": 200,
      "max": 800,
      "step": 50,
      "default": 400,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "range",
      "id": "content_gap",
      "label": "Content gap (desktop)",
      "min": 20,
      "max": 100,
      "step": 5,
      "default": 60,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "content_gap_mobile",
      "label": "Content gap (mobile)",
      "min": 10,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Padding top (desktop)",
      "min": 20,
      "max": 120,
      "step": 10,
      "default": 80,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Padding bottom (desktop)",
      "min": 20,
      "max": 120,
      "step": 10,
      "default": 80,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "label": "Padding top (mobile)",
      "min": 20,
      "max": 80,
      "step": 10,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "label": "Padding bottom (mobile)",
      "min": 20,
      "max": 80,
      "step": 10,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#4a4a4a"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#4a4a4a"
    },
    {
      "type": "color",
      "id": "button_background_color",
      "label": "Button background color",
      "default": "#4a9eda"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_hover_background_color",
      "label": "Button hover background color",
      "default": "#3c8bc7"
    },
    {
      "type": "color",
      "id": "button_hover_text_color",
      "label": "Button hover text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "guarantee_text_color",
      "label": "Guarantee text color",
      "default": "#666666"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "label": "Title font size (desktop)",
      "min": 20,
      "max": 60,
      "step": 2,
      "default": 36,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "title_size_mobile",
      "label": "Title font size (mobile)",
      "min": 16,
      "max": 40,
      "step": 2,
      "default": 24,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "description_size",
      "label": "Description font size (desktop)",
      "min": 12,
      "max": 24,
      "step": 1,
      "default": 16,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "description_size_mobile",
      "label": "Description font size (mobile)",
      "min": 12,
      "max": 20,
      "step": 1,
      "default": 14,
      "unit": "px"
    }
  ],
  "presets": [
    {
      "name": "Why Upstep?",
      "settings": {
        "title": "Why Upstep?",
        "description": "<p>We will use top-quality materials for your needs and produce your custom orthotics with the most advanced manufacturing methods!</p><p>Then we will send your custom orthotics via FedEx, to your doorstep.</p>",
        "button_text": "TAKE THE QUIZ",
        "guarantee_text": "180-day money-back guarantee",
        "image_width": 500,
        "image_height": 400
      }
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %} 