{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{%- liquid
  assign number = section.settings.items_width | at_most: section.blocks.size
  case number
    when 1
      assign width_class = 'width-100'
    when 2
      assign width_class = 'width-50'
    when 3
      assign width_class = 'width-33'
    when 4
      assign width_class = 'width-25'
  endcase

  if section.settings.show_link and section.settings.link_text != empty and section.settings.link_url != blank
    assign link = true
  endif

  if section.settings.title != empty or section.settings.text != empty
    assign show_header = true
  elsif link and section.settings.text_alignment == 'start'
    assign show_header = true
  endif

  if section.settings.title != empty or section.settings.text != empty
    assign container_div = true
  endif

  capture title_classes
    echo 'w720 '
    if section.settings.text_alignment == 'center'
      echo ' text-center align-center'
    endif
  endcapture
-%}

{%- if link %}
  {%- capture link -%}
    {%- liquid
      assign button_color = section.settings.button_style | split: ' ' | first
      assign button_style = section.settings.button_style | split: ' ' | last
      assign is_link = false
      if button_style == 'link'
        assign is_link = true
      endif
    -%}
    <p class="class-x link{% unless is_link %}-btn{% endunless %}"><a href="{{ section.settings.link_url }}" class="overlay-{{ button_color }} {% if is_link %}inline strong{% elsif button_style == 'inv' %}inv{% endif %}">{% if is_link %}<span>{% endif %}{{ section.settings.link_text }}{% if is_link %}</span>&nbsp;<i aria-hidden="true" class="icon-chevron-right"></i>{% endif %}</a></p>
  {%- endcapture %}
{%- endif -%}

<article>
  {%- if show_header -%}
    <header
      class="
        cols{% if link and section.settings.show_link == false %} align-middle{% endif %}{% if section.settings.title == empty and section.settings.text == empty %} text-end{% endif %} title-styling
        {% if section.settings.title_underline_style != 'none' %}
          title-underline-none
          {% if section.settings.title_underline_style contains 'accent' %}
            title-underline-accent
          {% elsif section.settings.title_underline_style contains 'gradient' %}
            title-underline-gradient
          {% endif %}
          {% if section.settings.title_underline_style contains 'secondary_font' %}
            title-underline-secondary-font
          {% endif %}
        {% endif %}
      "
      style="font-size:{{ section.settings.font_size }};"
    >
      {%- if container_div -%}<div class="{{ title_classes }}">{%- endif -%}
      {%- if section.settings.title != blank -%}
        {{ section.settings.title }}
      {%- endif -%}
      {%- if section.settings.text != empty -%}
        {{ section.settings.text }}
      {%- endif -%}
      {%- if container_div -%}</div>{%- endif -%}
      {%- if link and section.settings.text_alignment == 'start' -%}
        {{ link | replace: 'class-x', 'mobile-hide' }}
      {%- endif -%}
    </header>
  {%- endif -%}
  {%- if section.blocks.size > 0 -%}
    <ul class="l4st slider-fraction text-{{ section.settings.blocks_text_alignment }} mobile-text-{{ section.settings.mobile_blocks_text_alignment }} {{ width_class }}{% if section.settings.layout_mobile == 'rows' %} static{% endif %}">
      {%- for block in section.blocks -%}
        <li class="block-{{ block.id }}{% if block.settings.link_color == 'inherit' %} m6tx{% endif %}" {{ block.shopify_attributes }}>
          {% if block.settings.image or block.settings.image_svg or block.settings.icon != 'none' -%}
            <figure class="no-bd-radius">
              {%- if block.settings.show_link and block.settings.link_url != blank -%}
                <a
                  href="{{ block.settings.link_url }}"
                  aria-label="{{ block.settings.title | escape | default: block.settings.image.alt | default: "Multicolumn" }}"
                >
              {%- endif -%}
              {%- assign image_height = block.settings.image_height -%}
              {%- assign image_height_2 = block.settings.image_height | times: 2 -%}
              {% if block.settings.image or block.settings.image_svg %}
                <img
                  {% if block.settings.image_svg %}
                    src="{{ block.settings.image_svg }}"
                  {% elsif block.settings.image %}
                    src="{{ block.settings.image | image_url: height: image_height }}"
                    srcset="{{ block.settings.image | image_url: height: image_height }} 1x,{{ block.settings.image | image_url: height: image_height_2 }} 2x"
                  {% endif %}
                  style="height:{{ image_height }}px!important"
                  height="{{ image_height }}"
                  width="auto"
                  alt="{{ block.settings.image.alt | default: block.settings.title | escape }}"
                  loading="{% if section.index > 1 or forloop.first == false %}lazy{% else %}eager{% endif %}"
                >
              {%- elsif block.settings.icon != 'none' -%}
                {%- render 'icons', icon: block.settings.icon -%}
                <style>
                  #shopify-section-{{ section.id }} .block-{{ block.id }} svg { height: {{ image_height }}px!important; width: auto!important; }
                </style>
              {% endif %}
              {%- if block.settings.show_link and block.settings.link_url != blank -%}
                </a>
              {%- endif -%}
            </figure>
          {%- endif -%}
          {%- if block.settings.title != empty -%}
            <{{ section.settings.blocks_title_size }} class="ff-{{ section.settings.blocks_title_font }} {% if section.settings.enable_custom_text_styling %}fw-{{ section.settings.blocks_title_weight }}{% endif %}">
              {{- block.settings.title -}}
            </{{ section.settings.blocks_title_size }}>
          {%- endif -%}
          {%- if block.settings.text != empty -%}{{ block.settings.text }}{%- endif -%}
          {%- if block.settings.show_link and block.settings.link_text != empty and block.settings.link_url != blank -%}
            {%- liquid
              assign button_color = block.settings.button_style | split: ' ' | first
              assign button_style = block.settings.button_style | split: ' ' | last
              assign is_link = false
              if button_style == 'link'
                assign is_link = true
              endif
            -%}
            <p class="link{% unless is_link %}-btn{% endunless %}">
              <a
                href="{{ block.settings.link_url }}"
                class="overlay-{{ button_color }} {% if is_link %}strong inline{% elsif button_style == 'inv' %}inv{% endif %}"
              >
                {% if is_link %}<span>{% endif -%}
                {{- block.settings.link_text -}}
                {%- if is_link %}</span>&nbsp;<i aria-hidden="true" class="icon-chevron-right"></i>{% endif %}
              </a>
            </p>
          {%- endif %}
        </li>
      {%- endfor -%}
    </ul>
  {%- endif -%}
  {%- if link and section.settings.text_alignment == 'center' -%}
    {{ link | replace: 'class-x', 'm0 text-center' }}
  {%- elsif link and section.settings.text_alignment == 'start' -%}
    {{ link | replace: 'class-x', 'm0 mobile-only' }}
  {%- endif -%}
</article>

<style>
  #shopify-section-{{ section.id }} { position: relative; z-index: {{ section.settings.fix_zindex }}!important; }
  #shopify-section-{{ section.id }} .l4st figure { display: flex!important; }
  @media only screen and (min-width: 47.5em) {
  {% if link and section.settings.text_alignment == 'center' %}
    #shopify-section-{{ section.id }} { margin-bottom: {{ section.settings.spacing_desktop }}px; }
  {% else %}
    #shopify-section-{{ section.id }} .l4st { margin-bottom: {{ section.settings.spacing_desktop | minus: 36 }}px; }
  {% endif %}
  }
  @media only screen and (max-width: 47.5em) {
  {% if link %}
    #shopify-section-{{ section.id }} { margin-bottom: {{ section.settings.spacing_mobile }}px; }
  {% else %}
    #shopify-section-{{ section.id }} .l4st { margin-bottom: {{ section.settings.spacing_mobile }}px; }
  {% endif %}
  }
</style>

{% schema %}
{
  "name": "t:sections.icon_text_blocks.name",
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "range",
      "id": "items_width",
      "label": "t:sections.icon_text_blocks.settings.items_width.label",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 4
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.icon_text_blocks.settings.text_alignment.label",
      "options": [
        {
          "value": "start",
          "label": "t:sections.icon_text_blocks.settings.text_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.icon_text_blocks.settings.text_alignment.options__2.label"
        }
      ],
      "default": "start"
    },
    {
      "type": "header",
      "content": "t:global.typography.title.label"
    },
    {
      "type": "richtext",
      "id": "title",
      "label": "t:global.typography.title.label",
      "info": "t:global.typography.title.info",
      "default": "<h2>Multicolumn</h2>"
    },
    {
      "type": "select",
      "id": "title_underline_style",
      "label": "t:global.typography.title_underline_style.label",
      "options": [
        {
          "value": "none",
          "label": "t:global.typography.title_underline_style.none.label"
        },
        {
          "value": "secondary_font",
          "label": "t:global.typography.title_underline_style.secondary_font.label",
          "group": "t:global.typography.title_underline_style.group.fonts"
        },
        {
          "value": "secondary_font_accent",
          "label": "t:global.typography.title_underline_style.secondary_font_accent.label",
          "group": "t:global.typography.title_underline_style.group.fonts"
        },
        {
          "value": "secondary_font_gradient",
          "label": "t:global.typography.title_underline_style.secondary_font_gradient.label",
          "group": "t:global.typography.title_underline_style.group.fonts"
        },
        {
          "value": "accent",
          "label": "t:global.typography.title_underline_style.accent.label",
          "group": "t:global.typography.title_underline_style.group.colors"
        },
        {
          "value": "gradient",
          "label": "t:global.typography.title_underline_style.gradient.label",
          "group": "t:global.typography.title_underline_style.group.colors"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:global.typography.text.header"
    },
    {
      "id": "text",
      "type": "richtext",
      "label": "t:global.typography.text.label"
    },
    {
      "type": "select",
      "id": "font_size",
      "label": "t:global.typography.font_size.label",
      "options": [
        {
          "value": "13px",
          "label": "t:global.typography.font_size.13px.label"
        },
        {
          "value": "14px",
          "label": "t:global.typography.font_size.14px.label"
        },
        {
          "value": "16px",
          "label": "t:global.typography.font_size.16px.label"
        },
        {
          "value": "18px",
          "label": "t:global.typography.font_size.18px.label"
        }
      ],
      "default": "16px"
    },
    {
      "type": "header",
      "content": "t:global.button.header"
    },
    {
      "id": "show_link",
      "type": "checkbox",
      "label": "t:global.button.show_link.label",
      "default": true
    },
    {
      "id": "link_text",
      "type": "text",
      "label": "t:global.button.link_text.label",
      "default": "Button",
      "visible_if": "{{ section.settings.show_link }}"
    },
    {
      "id": "link_url",
      "type": "url",
      "label": "t:global.button.link_url.label",
      "visible_if": "{{ section.settings.show_link }}"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "t:global.button.button_style.label",
      "options": [
        {
          "value": "primary plain",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "secondary plain",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "tertiary plain",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "primary inv",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "secondary inv",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "tertiary inv",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "primary link",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.link"
        },
        {
          "value": "secondary link",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.link"
        },
        {
          "value": "tertiary link",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.link"
        }
      ],
      "default": "primary plain",
      "visible_if": "{{ section.settings.show_link }}"
    },
    {
      "type": "header",
      "content": "t:sections.icon_text_blocks.settings.blocks.header"
    },
    {
      "type": "select",
      "id": "blocks_text_alignment",
      "label": "t:sections.icon_text_blocks.settings.blocks.blocks_text_alignment.label",
      "info": "t:sections.icon_text_blocks.settings.blocks.blocks_text_alignment.info",
      "options": [
        {
          "value": "start",
          "label": "t:sections.icon_text_blocks.settings.blocks.blocks_text_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.icon_text_blocks.settings.blocks.blocks_text_alignment.options__2.label"
        }
      ],
      "default": "start"
    },
    {
      "type": "select",
      "id": "blocks_title_font",
      "label": "t:global.typography.title_font.label",
      "options": [
        {
          "value": "primary",
          "label": "t:global.typography.title_font.primary.label"
        },
        {
          "value": "secondary",
          "label": "t:global.typography.title_font.secondary.label"
        }
      ],
      "default": "primary"
    },
    {
      "type": "select",
      "id": "blocks_title_size",
      "label": "t:sections.icon_text_blocks.settings.blocks.blocks_title_size.label",
      "options": [
        {
          "value": "h1",
          "label": "t:global.typography.title_size.h1.label"
        },
        {
          "value": "h2",
          "label": "t:global.typography.title_size.h2.label"
        },
        {
          "value": "h3",
          "label": "t:global.typography.title_size.h3.label"
        },
        {
          "value": "h4",
          "label": "t:global.typography.title_size.h4.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "header",
      "content": "t:global.mobile.header"
    },
    {
      "type": "select",
      "id": "layout_mobile",
      "label": "t:sections.icon_text_blocks.settings.mobile.layout_mobile.label",
      "options": [
        {
          "value": "slider",
          "label": "t:sections.icon_text_blocks.settings.mobile.layout_mobile.options__1.label"
        },
        {
          "value": "rows",
          "label": "t:sections.icon_text_blocks.settings.mobile.layout_mobile.options__2.label"
        }
      ],
      "default": "slider"
    },
    {
      "type": "select",
      "id": "mobile_blocks_text_alignment",
      "label": "t:sections.icon_text_blocks.settings.blocks.blocks_text_alignment.label",
      "options": [
        {
          "value": "start",
          "label": "t:global.alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:global.alignment.center.label"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "t:global.spacing.header"
    },
    {
      "id": "spacing_desktop",
      "type": "range",
      "label": "t:global.spacing.spacing_desktop.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 50
    },
    {
      "id": "spacing_mobile",
      "type": "range",
      "label": "t:global.spacing.spacing_mobile.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 30
    },
    {
      "id": "fix_zindex",
      "type": "range",
      "label": "t:global.fix_zindex.label",
      "info": "t:global.fix_zindex.info",
      "min": 0,
      "max": 25,
      "step": 1,
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "t:sections.icon_text_blocks.blocks.text.name",
      "settings": [
        {
          "id": "icon",
          "type": "select",
          "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.label",
          "info": "t:sections.icon_text_blocks.blocks.text.settings.icon.info",
          "options": [
            {
              "value": "none",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__1.label"
            },
            {
              "value": "group",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__2.label"
            },
            {
              "value": "notification",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__3.label"
            },
            {
              "value": "cloud_data",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__4.label"
            },
            {
              "value": "verified",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__5.label"
            },
            {
              "value": "truck",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__6.label"
            },
            {
              "value": "image_placeholder",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__7.label"
            },
            {
              "value": "help_call",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__8.label"
            },
            {
              "value": "filters",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__9.label"
            },
            {
              "value": "shopping_bag",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__10.label"
            },
            {
              "value": "global_shipping",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__11.label"
            },
            {
              "value": "barcode",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__12.label"
            },
            {
              "value": "delivery_box_1",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__13.label"
            },
            {
              "value": "delivery_box_2",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__14.label"
            },
            {
              "value": "statistic",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__15.label"
            },
            {
              "value": "review",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__16.label"
            },
            {
              "value": "email",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__17.label"
            },
            {
              "value": "coin",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__18.label"
            },
            {
              "value": "24_hour_clock",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__19.label"
            },
            {
              "value": "question",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__20.label"
            },
            {
              "value": "24_7_call",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__21.label"
            },
            {
              "value": "speech_bubbles",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__22.label"
            },
            {
              "value": "coupon",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__23.label"
            },
            {
              "value": "mobile_payment",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__24.label"
            },
            {
              "value": "calculator",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__25.label"
            },
            {
              "value": "secure",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.icon.options__26.label"
            }
          ],
          "default": "none"
        },
        {
          "id": "image",
          "type": "image_picker",
          "label": "t:sections.icon_text_blocks.blocks.text.settings.image.label",
          "info": "t:sections.icon_text_blocks.blocks.text.settings.image.info"
        },
        {
          "id": "image_svg",
          "type": "url",
          "label": "t:sections.icon_text_blocks.blocks.text.settings.image_svg.label",
          "info": "t:sections.icon_text_blocks.blocks.text.settings.image_svg.info"
        },
        {
          "type": "range",
          "id": "image_height",
          "label": "t:sections.icon_text_blocks.blocks.text.settings.image_height.label",
          "min": 0,
          "max": 180,
          "step": 5,
          "unit": "px",
          "default": 55
        },
        {
          "id": "title",
          "type": "inline_richtext",
          "label": "t:sections.icon_text_blocks.blocks.text.settings.title.label",
          "default": "Column"
        },
        {
          "id": "text",
          "type": "richtext",
          "label": "t:sections.icon_text_blocks.blocks.text.settings.text.label",
          "default": "<p>Pair text with an icon or image to focus on services, products, blog posts or collections.</p>"
        },
        {
          "id": "link_color",
          "type": "select",
          "label": "t:sections.icon_text_blocks.blocks.text.settings.link_color.label",
          "options": [
            {
              "value": "inherit",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.link_color.options__1.label"
            },
            {
              "value": "default",
              "label": "t:sections.icon_text_blocks.blocks.text.settings.link_color.options__2.label"
            }
          ],
          "default": "inherit"
        },
        {
          "type": "header",
          "content": "t:global.button.header"
        },
        {
          "id": "show_link",
          "type": "checkbox",
          "label": "t:global.button.show_link.label",
          "default": true
        },
        {
          "id": "link_text",
          "type": "text",
          "label": "t:global.button.link_text.label",
          "default": "Button",
          "visible_if": "{{ block.settings.show_link }}"
        },
        {
          "id": "link_url",
          "type": "url",
          "label": "t:global.button.link_url.label",
          "visible_if": "{{ block.settings.show_link }}"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:global.button.button_style.label",
          "options": [
            {
              "value": "primary plain",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "secondary plain",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "tertiary plain",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "primary inv",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "secondary inv",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "tertiary inv",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "primary link",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "secondary link",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "tertiary link",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.link"
            }
          ],
          "default": "primary plain",
          "visible_if": "{{ block.settings.show_link }}"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.icon_text_blocks.presets.name",
      "blocks": [
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        }
      ]
    }
  ]
}
{% endschema %}
