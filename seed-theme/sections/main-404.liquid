<article class="palette-{{ section.settings.color_palette }} m6er module-color-palette">
	<h1><span class="strong">{{ '404.404' | t }}</span> {{ '404.title' | t }}</h1>
	<p>{{ '404.subtitle_html' | t }}</p>
    {%- if section.settings.show_link and section.settings.link_text != empty and section.settings.link_url != blank -%}
      {%- liquid
        assign button_color = section.settings.button_style | split: ' ' | first
        assign button_style = section.settings.button_style | split: ' ' | last
        assign is_link = false
        if button_style == 'link'
          assign is_link = true
        endif
      -%}
      <p class="link{% unless is_link %}-btn{% endunless %}">
        <a href="{{ section.settings.link_url }}" class="overlay-{{ button_color }} {% if is_link %}strong inline{% elsif button_style == 'inv' %}inv{% endif %}">
        {% if is_link %}<span>{% endif %}{{ section.settings.link_text }}{% if is_link %}</span>&nbsp;<i aria-hidden="true" class="icon-chevron-right"></i>{% endif %}
        </a>
      </p>
    {%- endif %}
</article>
<figure id="background" class="palette-{{ section.settings.color_palette }} module-color-palette static">
  <span class="img-overlay" style="opacity:{{ section.settings.overlay_opacity | divided_by: 100.0 }}"></span>
	{%- if section.settings.video -%}
      {{ section.settings.video | video_tag: autoplay: true, loop: true, muted: true, controls: false }}
	{%- endif -%}
	{%- if section.settings.image -%}
		{%- assign default_alt = 'header.extra_img_default_alt' | t %}
		<picture {% if section.settings.video %}class="mobile-only"{% endif %}>
	      <img
			src="{{ section.settings.image | image_url: width: 1400 }}"
	        srcset="{% render 'image-srcset', image: section.settings.image %}"
	        sizes="100vw"
	        width="1400"
	        height="846"
			alt="{{ section.settings.image.alt | default: default_alt | escape }}"
            style="object-position: {{ section.settings.image.presentation.focal_point }}"
            loading="lazy"
	      >
		</picture>
	{% endif %}
</figure>

{% schema %}
{
  "name": "t:main.404.name",
  "class": "align-center",
  "settings": [
    {
      "id": "image",
      "type": "image_picker",
      "label": "t:main.404.settings.image.label",
      "info": "t:main.404.settings.image.info"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "t:main.404.settings.overlay_opacity.label",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "default": 0
    },
    {
      "type": "color_scheme",
      "id": "color_palette",
      "label": "t:global.color_palette.label",
      "default": "scheme-5"
    },
    {
      "id": "video",
      "type": "video",
      "label": "t:main.404.settings.video.label",
      "info": "t:main.404.settings.video.info"
    },
    {
      "type": "header",
      "content": "t:global.button.header"
    },
    {
      "id": "show_link",
      "type": "checkbox",
      "label": "t:global.button.show_link.label",
      "default": true
    },
    {
      "id": "link_text",
      "type": "text",
      "label": "t:global.button.link_text.label",
      "default": "Button",
      "visible_if": "{{ section.settings.show_link }}"
    },
    {
      "id": "link_url",
      "type": "url",
      "label": "t:global.button.link_url.label",
      "visible_if": "{{ section.settings.show_link }}"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "t:global.button.button_style.label",
      "options": [
        {
          "value": "primary plain",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "secondary plain",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "tertiary plain",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "primary inv",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "secondary inv",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "tertiary inv",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "primary link",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.link"
        },
        {
          "value": "secondary link",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.link"
        },
        {
          "value": "tertiary link",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.link"
        }
      ],
      "default": "primary plain",
      "visible_if": "{{ section.settings.show_link }}"
    }
  ]
}
{% endschema %}
