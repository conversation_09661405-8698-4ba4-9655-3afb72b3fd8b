{{ 'section-quiz-banner.css' | asset_url | stylesheet_tag }}

<div class="quiz-banner-section palette-{{ section.settings.color_palette }}" 
     style="padding-top: {{ section.settings.spacing_desktop }}px; padding-bottom: {{ section.settings.spacing_desktop }}px;">
  <div class="quiz-banner-content">
    {% if section.settings.title != blank %}
      <h2 class="quiz-banner-title">{{ section.settings.title }}</h2>
    {% endif %}
    
    {% if section.settings.button_text != blank %}
      <div class="quiz-banner-button-wrapper">
        {%- liquid
          assign button_url = section.settings.button_url
          if button_url == blank
            assign button_url = '#'
          endif
        -%}
        <a href="{{ button_url }}" class="quiz-banner-button">
          {{ section.settings.button_text }}
        </a>
      </div>
    {% endif %}
  </div>
</div>

<style>
  #shopify-section-{{ section.id }} { 
    position: relative; 
    z-index: {{ section.settings.fix_zindex }}!important; 
  }
  
  @media only screen and (max-width: 47.5em) {
    #shopify-section-{{ section.id }} .quiz-banner-section {
      padding-top: {{ section.settings.spacing_mobile }}px !important;
      padding-bottom: {{ section.settings.spacing_mobile }}px !important;
    }
  }
</style>

{% schema %}
{
  "name": "Quiz Banner",
  "class": "shopify-section-quiz-banner",
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_palette",
      "label": "Color Scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Not Sure Which Upsteps You Need? Take our quiz"
    },
    {
      "type": "header",
      "content": "Button"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "FIND MY UPSTEPS"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button Link"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button Style",
      "options": [
        {
          "value": "primary plain",
          "label": "Primary",
          "group": "Plain"
        },
        {
          "value": "secondary plain",
          "label": "Secondary",
          "group": "Plain"
        },
        {
          "value": "tertiary plain",
          "label": "Tertiary",
          "group": "Plain"
        },
        {
          "value": "primary inv",
          "label": "Primary",
          "group": "Inverted"
        },
        {
          "value": "secondary inv",
          "label": "Secondary",
          "group": "Inverted"
        },
        {
          "value": "tertiary inv",
          "label": "Tertiary",
          "group": "Inverted"
        }
      ],
      "default": "secondary plain"
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Desktop Spacing",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "label": "Mobile Spacing",
      "default": 60
    },
    {
      "type": "range",
      "id": "fix_zindex",
      "label": "Fix Z-Index",
      "info": "Adjust if section appears behind other elements",
      "min": 0,
      "max": 25,
      "step": 1,
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Quiz Banner",
      "settings": {
        "title": "Not Sure Which Upsteps You Need? Take our quiz",
        "button_text": "FIND MY UPSTEPS",
        "button_style": "secondary plain"
      }
    }
  ],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
