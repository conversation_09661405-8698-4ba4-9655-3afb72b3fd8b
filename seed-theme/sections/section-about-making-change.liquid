{{ 'about-us-styles.css' | asset_url | stylesheet_tag }}

<div class="about-making-change-section">
  <div class="about-making-change-content">
    {% if section.settings.title != blank %}
      <h2 class="section-title">{{ section.settings.title }}</h2>
    {% endif %}

    <div class="features-grid">
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'feature' %}
            <div class="feature-item" {{ block.shopify_attributes }}>
              <div class="feature-icon">
                {% if block.settings.svg_code != blank %}
                  <div class="custom-svg">
                    {{ block.settings.svg_code }}
                  </div>
                {% else %}
                  <div class="icon-placeholder"></div>
                {% endif %}
              </div>

              {% if block.settings.description != blank %}
                <p class="feature-description" {% if block.settings.description_color != blank %}style="color: {{ block.settings.description_color }}"{% endif %}>{{ block.settings.description }}</p>
              {% endif %}
            </div>
        {% endcase %}
      {% endfor %}
    </div>
  </div>
</div>



{% schema %}
{
  "name": "About Making Change",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Making A Change"
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "label": "Desktop Spacing",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "label": "Mobile Spacing",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "px",
      "default": 60
    }
  ],
  "blocks": [
    {
      "type": "feature",
      "name": "Feature",
      "settings": [
        {
          "type": "html",
          "id": "svg_code",
          "label": "SVG Icon Code",
          "info": "Paste your SVG code here (recommended size: 80x80px)"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Feature Description"
        },
        {
          "type": "color",
          "id": "description_color",
          "label": "Description Color",
          "default": "rgb(74, 74, 74)"
        }
      ]
    }
  ]
}
{% endschema %}
