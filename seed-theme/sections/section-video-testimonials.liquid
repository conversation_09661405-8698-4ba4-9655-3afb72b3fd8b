<!-- Video Testimonials Section -->
<section class="shopify-section-video-testimonials">
  <div class="video-testimonials-container">
    {%- if section.settings.title != blank -%}
      <h2 class="video-testimonials-title">{{ section.settings.title }}</h2>
    {%- endif -%}
    
    <div class="video-testimonials-grid" id="video-testimonials-grid">
      {%- for block in section.blocks -%}
        {%- if forloop.index == 1 -%}
          <div class="video-testimonial-item large" data-block-id="{{ block.id }}" data-video-index="{{ forloop.index0 }}">
        {%- else -%}
          <div class="video-testimonial-item small" data-block-id="{{ block.id }}" data-video-index="{{ forloop.index0 }}">
        {%- endif -%}

          {%- if block.settings.video_type == 'youtube' and block.settings.youtube_id != blank -%}
            <div class="video-thumbnail youtube-bg" onclick="playVideo(this, '{{ block.settings.youtube_id }}', 'youtube')" style="background-image: url('https://img.youtube.com/vi/{{ block.settings.youtube_id }}/maxresdefault.jpg');">
              <div class="play-button">
                <svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="16.5" cy="16.5" r="16.5" fill="white"/>
                  <path d="M13 11L22 16.5L13 22V11Z" fill="black"/>
                </svg>
              </div>

            </div>
          {%- elsif block.settings.video_type == 'shopify' and block.settings.shopify_video != blank -%}
            <div class="video-thumbnail shopify-bg" onclick="playVideo(this, '{{ block.settings.shopify_video }}', 'shopify')">
              <div class="play-button">
                <svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="16.5" cy="16.5" r="16.5" fill="white"/>
                  <path d="M13 11L22 16.5L13 22V11Z" fill="black"/>
                </svg>
              </div>

            </div>
          {%- endif -%}
        </div>
      {%- endfor -%}
    </div>

    <!-- Additional videos container (initially hidden) -->
    <div class="additional-videos-container" id="additional-videos" style="display: none;">

    </div>

    <div class="load-more-container">
      <button class="load-more-btn" id="load-more-toggle" onclick="toggleVideos()">
        {{ section.settings.load_more_text | default: "LOAD MORE" }}
      </button>
    </div>


  </div>
</section>

<style>
  .shopify-section-video-testimonials {
    background: #FFFFFF;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding: 40px 20px;
  }

  .shopify-section-video-testimonials .video-testimonials-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .shopify-section-video-testimonials .video-testimonials-title {
    color: #4A4A4A;
    text-align: center;
    font-family: Merriweather_, serif;
    font-size: 36px;
    font-style: normal;
    font-weight: 700;
    line-height: 50.4px;
    letter-spacing: 0.2px;
    margin: 0 0 50px 0;
  }

  .shopify-section-video-testimonials .video-testimonials-grid {
    display: grid;
    grid-template-columns: 581px 280px 280px;
    grid-template-rows: 280px 280px;
    gap: 20px;
    justify-content: center;
    margin-bottom: 40px;
  }

  .shopify-section-video-testimonials .video-testimonial-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    background: #4A4A4A;
  }

  .shopify-section-video-testimonials .video-testimonial-item.large {
    grid-row: 1 / 3;
  }

  .shopify-section-video-testimonials .video-testimonial-item.small {
    /* Default grid placement */
  }

  .shopify-section-video-testimonials .video-thumbnail,
  .shopify-section-video-testimonials .video-placeholder {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    padding: 8px;
    box-sizing: border-box;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    will-change: transform;
    border-radius: 10px;
    overflow: hidden;
  }

  .shopify-section-video-testimonials .video-thumbnail:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .shopify-section-video-testimonials .video-thumbnail.youtube-bg {
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    padding: 0 !important;
    border-radius: 10px !important;
    overflow: hidden !important;
  }

  .shopify-section-video-testimonials .video-thumbnail img,
  .shopify-section-video-testimonials .video-thumbnail video {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: fill !important;
    border-radius: 10px !important;
  }

  .shopify-section-video-testimonials .play-button {
    position: relative;
    z-index: 2;
    width: 33px;
    height: 33px;
    cursor: pointer;
    transition: opacity 0.3s ease, transform 0.2s ease;
    will-change: transform, opacity;
  }

  .shopify-section-video-testimonials .play-button:hover {
    transform: scale(1.1);
    opacity: 0.8;
  }

  .shopify-section-video-testimonials iframe,
  .shopify-section-video-testimonials video[controls] {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border-radius: 10px !important;
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  .shopify-section-video-testimonials iframe,
  .shopify-section-video-testimonials video[controls] {
    opacity: 0;
    animation: fadeInVideo 0.3s ease forwards;
  }

  @keyframes fadeInVideo {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .shopify-section-video-testimonials .video-thumbnail.youtube-bg .play-button {
    position: absolute;
    bottom: 8px;
    right: 8px;
    z-index: 2;
  }

  .shopify-section-video-testimonials .play-button:hover {
    opacity: 0.8;
  }

  .shopify-section-video-testimonials .load-more-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
    width: 100%;
  }

  .shopify-section-video-testimonials .load-more-btn {
    display: flex;
    width: 250px;
    height: 54px;
    padding: 9px 20px;
    justify-content: center;
    align-items: center;
    gap: 6px;
    border-radius: 41px;
    border: 1px solid var(--Primary04, #009EE0);
    background: none;
    color: var(--Primary04, #009EE0);
    font-family: Lato_, sans-serif;
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
  }

  .shopify-section-video-testimonials .load-more-btn:hover {
    background: rgba(0, 158, 224, 0.1);
    border-color: var(--Primary04, #009EE0);
    color: var(--Primary04, #009EE0);
    transition: none;
  }

  .shopify-section-video-testimonials .load-more-btn:before {
    display: none !important;
  }

  .shopify-section-video-testimonials .additional-videos-container {
    display: grid;
    grid-template-columns: 280px 280px 581px;
    grid-template-rows: 280px 280px;
    gap: 20px;
    justify-content: center;
    margin-bottom: 40px;
    max-width: 1141px;
    margin-left: auto;
    margin-right: auto;
  }

  .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item:first-child {
    grid-row: 1 / 3;
    grid-column: 3;
    height: 580px;
  }

  .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item:not(:first-child) {
    height: 280px;
  }

  .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item:nth-child(2) {
    grid-row: 1;
    grid-column: 1;
  }

  .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item:nth-child(3) {
    grid-row: 2;
    grid-column: 1;
  }

  .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item:nth-child(4) {
    grid-row: 1;
    grid-column: 2;
  }

  .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item:nth-child(5) {
    grid-row: 2;
    grid-column: 2;
  }

  .shopify-section-video-testimonials .video-row {
    display: grid;
    grid-template-columns: 280px 280px 581px;
    grid-template-rows: 280px 280px;
    gap: 20px;
    justify-content: center;
    margin-bottom: 20px;
  }

  .shopify-section-video-testimonials .video-row .video-testimonial-item.large {
    grid-row: 1 / 3;
    grid-column: 3;
  }

  /* Mobile Responsive */
  @media (max-width: 1200px) {
    .shopify-section-video-testimonials .video-testimonials-grid {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto;
      max-width: 600px;
      margin: 0 auto 40px auto;
    }

    .shopify-section-video-testimonials .video-testimonial-item.large {
      grid-row: auto;
      grid-column: 1 / 3;
      height: 300px;
    }

    .shopify-section-video-testimonials .video-testimonial-item.small {
      height: 200px;
    }

    .shopify-section-video-testimonials .additional-videos-container {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto;
      max-width: 600px;
    }

    .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item:first-child {
      grid-row: auto;
      grid-column: 1 / 3;
      height: 300px;
    }

    .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item:not(:first-child) {
      height: 200px;
      grid-row: auto;
      grid-column: auto;
    }

    .shopify-section-video-testimonials .video-row {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto;
      max-width: 600px;
      margin: 0 auto 20px auto;
    }

    .shopify-section-video-testimonials .video-row .video-testimonial-item.large {
      grid-row: auto;
      grid-column: 1 / 3;
      height: 300px;
    }
  }

  @media (max-width: 768px) {
    .shopify-section-video-testimonials {
      padding: 40px 16px;
    }

    .shopify-section-video-testimonials .video-testimonials-title {
      font-size: 28px;
      line-height: 38px;
      margin-bottom: 30px;
    }

    .shopify-section-video-testimonials .video-testimonials-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      grid-auto-flow: dense;
    }

    .shopify-section-video-testimonials .video-testimonial-item.large,
    .shopify-section-video-testimonials .video-testimonial-item.small {
      grid-column: unset !important;
      height: 200px !important;
    }

    .shopify-section-video-testimonials .video-testimonial-item:nth-child(3n+1) {
      grid-column: 1 / -1 !important;
      height: 300px !important;
    }

    .shopify-section-video-testimonials .video-testimonial-item.expanded {
      grid-column: 1 / -1 !important;
      height: 300px !important;
      transition: all 0.3s ease;
    }

    .shopify-section-video-testimonials .additional-videos-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-top: 16px;
      grid-auto-flow: dense;
    }


    .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item {
      height: 200px !important;
    }

    .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item:nth-child(3n+1) {
      grid-column: 1 / -1 !important;
      height: 300px !important;
    }


    .shopify-section-video-testimonials .additional-videos-container .video-testimonial-item.expanded {
      grid-column: 1 / -1 !important;
      height: 300px !important;
      transition: all 0.3s ease;
    }

    .shopify-section-video-testimonials .video-row {
      grid-template-columns: 1fr;
    }

    .shopify-section-video-testimonials .video-row .video-testimonial-item.large {
      grid-column: 1;
      height: 300px;
    }
  }

  @media (max-width: 480px) {
    .shopify-section-video-testimonials .video-testimonials-title {
      font-size: 24px;
      line-height: 32px;
    }

    .shopify-section-video-testimonials .video-testimonial-item.large,
    .shopify-section-video-testimonials .video-testimonial-item.small {
      height: 250px;
    }
  }
</style>

<script>
  let isExpanded = false;
  let currentActiveVideo = null;
  let allVideoContainers = [];
  let currentlyShownVideos = 0; // Track how many videos are currently shown

  // Mobile detection and video count settings
  const desktopInitialCount = {{ section.settings.initial_videos_count }};
  const mobileInitialCount = {{ section.settings.initial_videos_count_mobile }};

  function isMobileDevice() {
    return window.innerWidth <= 768;
  }

  function getCurrentInitialCount() {
    return isMobileDevice() ? mobileInitialCount : desktopInitialCount;
  }

  document.addEventListener('DOMContentLoaded', function() {
    initializeVideoSystem();
    setupVisibilityHandlers();
    setupIntersectionObserver();
    updateVideoDisplay();
  });

  function initializeVideoSystem() {
    allVideoContainers = document.querySelectorAll('.video-testimonial-item');
    setupVisibilityHandlers();
  }

  function updateVideoDisplay() {
    const grid = document.getElementById('video-testimonials-grid');
    const additionalVideos = document.getElementById('additional-videos');
    const loadMoreContainer = document.querySelector('.load-more-container');
    const allVideos = Array.from(document.querySelectorAll('.video-testimonial-item'));
    const currentCount = getCurrentInitialCount();

    // Show/hide videos based on current device
    allVideos.forEach((video, index) => {
      if (index < currentCount) {
        video.style.display = 'block';
        // Ensure video is in main grid
        if (!grid.contains(video)) {
          grid.appendChild(video);
        }
      } else {
        video.style.display = 'none';
        // Move extra videos to additional container
        if (!additionalVideos.contains(video)) {
          additionalVideos.appendChild(video);
        }
      }
    });

    // Update currently shown videos counter
    currentlyShownVideos = currentCount;

    // Show/hide load more button based on remaining videos
    const totalVideos = allVideos.length;
    if (totalVideos > currentCount) {
      loadMoreContainer.style.display = 'flex';
    } else {
      loadMoreContainer.style.display = 'none';
    }

    // Reset expanded state
    isExpanded = false;
    additionalVideos.style.display = 'none';
    const toggleButton = document.getElementById('load-more-toggle');
    if (toggleButton) {
      toggleButton.textContent = '{{ section.settings.load_more_text | default: "LOAD MORE" }}';
    }
  }

  function setupVisibilityHandlers() {
    document.addEventListener('visibilitychange', function() {
      if (document.hidden) {
        pauseAllVideos();
      }
    });

    window.addEventListener('blur', function() {
      pauseAllVideos();
    });
  }

  function setupIntersectionObserver() {
    const observer = new IntersectionObserver(function(entries) {
      entries.forEach(function(entry) {
        const container = entry.target;
        const video = container.querySelector('video[controls]');
        const iframe = container.querySelector('iframe[src*="youtube.com"]');

        if (!entry.isIntersecting) {
          if (video && !video.paused) {
            video.pause();
          }
          if (iframe) {
            try {
              iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
            } catch (e) {
              console.log('Could not pause YouTube video:', e);
            }
          }
        }
      });
    }, {
      threshold: 0.5,
      rootMargin: '0px 0px -100px 0px'
    });

    allVideoContainers.forEach(function(container) {
      observer.observe(container);
    });

    window.videoIntersectionObserver = observer;
  }

  function stopAllOtherVideos(currentContainer) {
    allVideoContainers.forEach(function(container) {
      if (container !== currentContainer) {
        resetVideoContainer(container);
      }
    });
  }

  function resetVideoContainer(container) {
    const iframe = container.querySelector('iframe');
    const video = container.querySelector('video[controls]');

    if (iframe) {
      const blockId = container.getAttribute('data-block-id');
      const originalThumbnail = container.querySelector('.video-thumbnail');
      if (!originalThumbnail) {
        restoreOriginalThumbnail(container, blockId);
      }
      iframe.remove();
    }

    if (video && video.controls) {
      video.pause();
      video.currentTime = 0;
      video.remove();
      restoreOriginalThumbnail(container);
    }

    collapseExpandedVideo(container);
  }

  function restoreOriginalThumbnail(container, blockId) {
    const originalOnclick = container.getAttribute('data-original-onclick');
    const originalVideoType = container.getAttribute('data-video-type');
    const originalVideoSource = container.getAttribute('data-video-source');

    if (originalOnclick) {
      container.innerHTML = container.getAttribute('data-original-html') || getDefaultThumbnailHTML();
      const thumbnail = container.querySelector('.video-thumbnail');
      if (thumbnail) {
        thumbnail.setAttribute('onclick', originalOnclick);
      }
    } else {
      container.innerHTML = getDefaultThumbnailHTML();
    }
  }

  function getDefaultThumbnailHTML() {
    return `
      <div class="video-thumbnail">
        <div class="play-button">
          <svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="16.5" cy="16.5" r="16.5" fill="white"/>
            <path d="M13 11L22 16.5L13 22V11Z" fill="black"/>
          </svg>
        </div>
      </div>
    `;
  }


  function saveOriginalState(container) {
    if (!container.getAttribute('data-original-html')) {
      container.setAttribute('data-original-html', container.innerHTML);
      const thumbnail = container.querySelector('.video-thumbnail');
      if (thumbnail && thumbnail.getAttribute('onclick')) {
        container.setAttribute('data-original-onclick', thumbnail.getAttribute('onclick'));
      }
    }
  }

  function pauseAllVideos() {
    const videos = document.querySelectorAll('video[controls]');
    videos.forEach(function(video) {
      if (!video.paused) {
        video.pause();
      }
    });

    const iframes = document.querySelectorAll('iframe[src*="youtube.com"]');
    iframes.forEach(function(iframe) {
      try {
        iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
      } catch (e) {
        console.log('Could not pause YouTube video:', e);
      }
    });
  }

  function isSmallVideo(container) {
    const computedStyle = window.getComputedStyle(container);
    const gridColumn = computedStyle.getPropertyValue('grid-column');
    return gridColumn !== '1 / -1' && !container.classList.contains('expanded');
  }

  function expandSmallVideo(container) {
    if (isSmallVideo(container)) {
      container.classList.add('expanded');
      container.setAttribute('data-was-expanded', 'true');

      setTimeout(() => {
        container.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      }, 100);
    }
  }

  function collapseExpandedVideo(container) {
    if (container.getAttribute('data-was-expanded') === 'true') {
      container.classList.remove('expanded');
      container.removeAttribute('data-was-expanded');
    }
  }

  function playVideo(element, videoSource, videoType) {
    const container = element.parentNode;

    saveOriginalState(container);

    expandSmallVideo(container);

    stopAllOtherVideos(container);

    currentActiveVideo = container;

    if (videoType === 'youtube') {
      const iframe = document.createElement('iframe');
      iframe.src = `https://www.youtube.com/embed/${videoSource}?autoplay=1&rel=0&enablejsapi=1`;
      iframe.width = '100%';
      iframe.height = '100%';
      iframe.frameBorder = '0';
      iframe.allow = 'autoplay; encrypted-media';
      iframe.allowFullscreen = true;
      iframe.style.borderRadius = '10px';
      iframe.style.position = 'absolute';
      iframe.style.top = '0';
      iframe.style.left = '0';

      container.innerHTML = '';
      container.appendChild(iframe);
    } else if (videoType === 'shopify') {
      const video = document.createElement('video');
      video.src = videoSource;
      video.controls = true;
      video.autoplay = true;
      video.style.width = '100%';
      video.style.height = '100%';
      video.style.objectFit = 'cover';
      video.style.borderRadius = '10px';
      video.style.position = 'absolute';
      video.style.top = '0';
      video.style.left = '0';

      video.addEventListener('ended', function() {
        resetVideoContainer(container);
      });

      video.addEventListener('error', function() {
        console.error('Video loading error:', video.error);
        resetVideoContainer(container);
      });

      video.preload = 'metadata';
      video.addEventListener('loadstart', function() {
        const otherVideos = document.querySelectorAll('video[preload="auto"]');
        otherVideos.forEach(function(otherVideo) {
          if (otherVideo !== video) {
            otherVideo.preload = 'none';
          }
        });
      });

      container.innerHTML = '';
      container.appendChild(video);
    }
  }

  function toggleVideos() {
    const additionalVideos = document.getElementById('additional-videos');
    const toggleButton = document.getElementById('load-more-toggle');
    const loadMoreContainer = document.querySelector('.load-more-container');
    const currentCount = getCurrentInitialCount();
    const allVideos = Array.from(document.querySelectorAll('.video-testimonial-item'));
    const totalVideos = allVideos.length;

    if (!isExpanded) {
      pauseAllVideos();

      // Show next batch of videos
      const videosToShow = allVideos.slice(currentlyShownVideos, currentlyShownVideos + currentCount);

      videosToShow.forEach(video => {
        video.style.display = 'block';
        // Move video to additional container if not already there
        if (!additionalVideos.contains(video)) {
          additionalVideos.appendChild(video);
        }
      });

      // Update counter
      currentlyShownVideos += videosToShow.length;

      // Show additional videos container
      additionalVideos.style.display = 'grid';

      // Check if there are more videos to show
      if (currentlyShownVideos >= totalVideos) {
        // All videos are shown, show CLOSE button
        toggleButton.textContent = 'CLOSE';
        isExpanded = true;
      } else {
        // More videos available, keep LOAD MORE text
        toggleButton.textContent = '{{ section.settings.load_more_text | default: "LOAD MORE" }}';
      }

      // Move load more button after additional videos
      additionalVideos.parentNode.insertBefore(loadMoreContainer, additionalVideos.nextSibling);

      allVideoContainers = document.querySelectorAll('.video-testimonial-item');

      if (window.videoIntersectionObserver) {
        const newContainers = additionalVideos.querySelectorAll('.video-testimonial-item');
        newContainers.forEach(function(container) {
          window.videoIntersectionObserver.observe(container);
        });
      }
    } else {
      // Close/collapse all additional videos
      pauseAllVideos();

      // Hide all additional videos
      const additionalVideoItems = additionalVideos.querySelectorAll('.video-testimonial-item');
      additionalVideoItems.forEach(function(container) {
        resetVideoContainer(container);
        container.style.display = 'none';
      });

      // Reset state
      additionalVideos.style.display = 'none';
      toggleButton.textContent = '{{ section.settings.load_more_text | default: "LOAD MORE" }}';
      isExpanded = false;
      currentlyShownVideos = getCurrentInitialCount(); // Reset to initial count

      // Move load more button back to original position
      additionalVideos.parentNode.insertBefore(loadMoreContainer, additionalVideos);

      allVideoContainers = document.querySelectorAll('.video-testimonial-item');

      if (window.videoIntersectionObserver) {
        const hiddenContainers = additionalVideos.querySelectorAll('.video-testimonial-item');
        hiddenContainers.forEach(function(container) {
          window.videoIntersectionObserver.unobserve(container);
        });
      }
    }
  }

  window.addEventListener('beforeunload', function() {
    pauseAllVideos();
    cleanupAllVideoResources();
  });

  // Handle window resize to update video display
  let resizeTimeout;
  window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(function() {
      // Reset state when switching between devices
      isExpanded = false;
      updateVideoDisplay();
    }, 250);
  });

  function cleanupAllVideoResources() {
    allVideoContainers.forEach(function(container) {
      const iframe = container.querySelector('iframe');
      const video = container.querySelector('video[controls]');

      if (iframe) {
        try {
          iframe.contentWindow.postMessage('{"event":"command","func":"stopVideo","args":""}', '*');
        } catch (e) {
          console.log('Could not stop YouTube video:', e);
        }
        iframe.src = 'about:blank';
        iframe.remove();
      }

      if (video) {
        video.pause();
        video.currentTime = 0;
        video.src = '';
        video.load();
        video.remove();
      }

      resetVideoContainer(container);
    });
  }

  function monitorVideoPerformance() {
    const activeVideos = document.querySelectorAll('video[controls], iframe[src*="youtube.com"]');
    if (activeVideos.length > 1) {
      console.warn('Multiple videos are active. This may impact performance.');
    }

    if (performance.memory) {
      const memoryInfo = performance.memory;
      if (memoryInfo.usedJSHeapSize > memoryInfo.jsHeapSizeLimit * 0.8) {
        console.warn('High memory usage detected. Consider pausing unused videos.');
        pauseAllVideos();
      }
    }
  }

  setInterval(monitorVideoPerformance, 30000);
</script>

{% schema %}
{
  "name": "Video Testimonials",
  "tag": "section",
  "class": "section-video-testimonials",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Yes! They actually work, see for yourself..."
    },
    {
      "type": "range",
      "id": "initial_videos_count",
      "label": "Initial Videos Count (Desktop)",
      "min": 3,
      "max": 10,
      "step": 1,
      "default": 5
    },
    {
      "type": "range",
      "id": "initial_videos_count_mobile",
      "label": "Initial Videos Count (Mobile)",
      "min": 1,
      "max": 10,
      "step": 1,
      "default": 3
    },
    {
      "type": "text",
      "id": "load_more_text",
      "label": "Load More Button Text",
      "default": "LOAD MORE"
    }
  ],
  "blocks": [
    {
      "type": "video",
      "name": "Video Testimonial",
      "settings": [
        {
          "type": "select",
          "id": "video_type",
          "label": "Video Type",
          "options": [
            {
              "value": "youtube",
              "label": "YouTube"
            },
            {
              "value": "shopify",
              "label": "Shopify Upload"
            }
          ],
          "default": "youtube"
        },
        {
          "type": "text",
          "id": "youtube_id",
          "label": "YouTube Video ID",
          "info": "Enter only the video ID (e.g., dQw4w9WgXcQ)"
        },
        {
          "type": "url",
          "id": "shopify_video",
          "label": "Shopify Video File URL",
          "info": "Upload video to Files section and paste the URL here"
        },
        {
          "type": "text",
          "id": "video_title",
          "label": "Video Title (Optional)",
          "info": "For accessibility and SEO"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Video Testimonials",
      "blocks": [
        {
          "type": "video",
          "settings": {
            "video_title": "Customer Review 1"
          }
        },
        {
          "type": "video",
          "settings": {
            "video_title": "Customer Review 2"
          }
        },
        {
          "type": "video",
          "settings": {
            "video_title": "Customer Review 3"
          }
        },
        {
          "type": "video",
          "settings": {
            "video_title": "Customer Review 4"
          }
        },
        {
          "type": "video",
          "settings": {
            "video_title": "Customer Review 5"
          }
        }
      ]
    }
  ]
}
{% endschema %}
