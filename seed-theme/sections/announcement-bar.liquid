{%- if section.blocks.size > 0 -%}
  <div
    class="shopify-section-announcement-bar no-nav swiper"
    {% if section.settings.autoplay %}
      data-autoplay="{{ section.settings.autoplay_speed | times: 1000 }}"
    {% endif %}
    data-transition-speed="{{ section.settings.transition_speed | default: 600 }}"
  >
    <div class="swiper-wrapper">
      {%- for block in section.blocks -%}
        <div class="swiper-slide announcement-item" {{ block.shopify_attributes }}>
          <div class="announcement-content">
            {%- if block.settings.show_icon and block.settings.custom_svg != blank -%}
              <div class="announcement-icon">
                {{ block.settings.custom_svg }}
              </div>
            {%- endif -%}
            <div class="announcement-text">
              {%- if block.settings.title != blank -%}
                <span class="announcement-title">{{ block.settings.title }}</span>
              {%- endif -%}
              {%- if block.settings.text != blank -%}
                <span class="announcement-subtitle">
                  {{- block.settings.text | replace: '</p><p>', ' ' | remove: '<p>' | remove: '</p>' -}}
                </span>
              {%- endif -%}
            </div>
          </div>
        </div>
      {%- endfor -%}
    </div>
  </div>

  <style>
    /* Force immediate display with fixed height - override theme's default hidden state */
    .shopify-section-announcement-bar {
      display: block !important;
      background: linear-gradient(180deg, #061B5D 0%, #0575E6 258.54%);
      box-shadow: 0 4px 24px 0 rgba(74, 74, 74, 0.08);
      width: 100vw !important; /* Full viewport width */
      position: relative;
      left: 50%;
      right: 50%;
      margin-left: -50vw !important;
      margin-right: -50vw !important;
      height: 48px !important; /* Fixed height to prevent jumping */
      min-height: 48px !important;
      max-height: 48px !important;
      overflow: hidden;
      /* Ensure banner appears immediately without any entrance animations */
      opacity: 1 !important;
      visibility: visible !important;
      transform: none !important;
      animation: none !important;
      transition: none !important;
    }

    #shopify-section-{{ section.id }} {
      opacity: 1 !important;
      visibility: visible !important;
      transform: none !important;
      animation: none !important;
      transition: none !important;
    }

    html:not(.show-notice) .shopify-section-announcement-bar {
      display: block !important;
    }

    .shopify-section-announcement-bar .swiper-wrapper {
      height: 48px !important;
      align-items: center;
    }

    .shopify-section-announcement-bar .swiper-slide {
      height: 48px !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .shopify-section-announcement-bar.no-nav .swiper-button-prev,
    .shopify-section-announcement-bar.no-nav .swiper-button-next {
      display: none !important;
    }

    .shopify-section-announcement-bar a.close {
      color: white !important;
      opacity: 0.8;
      transition: opacity 0.2s ease;
    }

    .shopify-section-announcement-bar a.close:hover {
      opacity: 1;
    }

    .announcement-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .announcement-icon {
      flex-shrink: 0;
    }

    .announcement-icon svg {
      width: 19px;
      height: 18px;
    }

    .announcement-text {
      display: flex;
      align-items: center;
      gap: 8px;
      text-align: center;
    }

    .announcement-title {
      color: #ECF0F1;
      font-family: Lato_, sans-serif;
      font-size: 16px;
      font-weight: 400;
      line-height: 22.4px;
      letter-spacing: 0.2px;
      white-space: nowrap;
    }

    .announcement-subtitle {
      color: #ECF0F1;
      font-family: Lato_, sans-serif;
      font-size: 15px;
      font-weight: 400;
      line-height: 22.4px;
      letter-spacing: 0.2px;
      white-space: nowrap;
    }

    @media only screen and (max-width: 768px) {
      .announcement-content {
        gap: 8px;
        padding: 0 15px;
      }

      .announcement-text {
        flex-direction: column;
        gap: 2px;
      }

      .announcement-title {
        font-size: 14px;
        line-height: 20px;
      }

      .announcement-subtitle {
        font-size: 13px;
        line-height: 20px;
      }
    }
  </style>

  <script>
    window.restoreAnnouncementBanner = function() {
      document.cookie = "notice=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
      location.reload();
    };

    (function() {
      const announcementBar = document.querySelector('#shopify-section-{{ section.id }} .shopify-section-announcement-bar');
      if (!announcementBar) return;

      const slides = announcementBar.querySelectorAll('.swiper-slide');
      if (slides.length <= 1) return;

      const config = {
        autoplay: {{ section.settings.autoplay | json }},
        autoplaySpeed: {{ section.settings.autoplay_speed | times: 1000 }},
        transitionSpeed: {{ section.settings.transition_speed | default: 600 }},
        sectionId: '{{ section.id }}'
      };

      let currentIndex = 0;
      let isTransitioning = false;
      let autoplayInterval = null;
      let transitionTimeout = null;
      let isDestroyed = false;

      function initSlides() {
        slides.forEach((slide, index) => {
          slide.style.opacity = index === 0 ? '1' : '0';
          slide.style.transition = `opacity ${config.transitionSpeed}ms ease-in-out`;
          slide.style.position = 'absolute';
          slide.style.top = '0';
          slide.style.left = '0';
          slide.style.width = '100%';
          slide.style.height = '100%';
        });

        const wrapper = announcementBar.querySelector('.swiper-wrapper');
        if (wrapper) {
          wrapper.style.position = 'relative';
          wrapper.style.height = '48px';
        }
      }

      function showSlide(index) {
        if (isTransitioning || isDestroyed) return;
        isTransitioning = true;

        if (transitionTimeout) {
          clearTimeout(transitionTimeout);
          transitionTimeout = null;
        }

        slides[currentIndex].style.opacity = '0';

        transitionTimeout = setTimeout(() => {
          if (isDestroyed) return;

          slides[index].style.opacity = '1';
          currentIndex = index;
          isTransitioning = false;

          if (window.location.search.includes('debug')) {
          }
        }, config.transitionSpeed / 2);
      }

      function nextSlide() {
        if (isDestroyed) return;
        const nextIndex = (currentIndex + 1) % slides.length;
        showSlide(nextIndex);
      }

      function startAutoplay() {
        if (!config.autoplay || isDestroyed) return;

        stopAutoplay();

        autoplayInterval = setInterval(nextSlide, config.autoplaySpeed);

        if (window.location.search.includes('debug')) {
          console.log(`Announcement autoplay started: ${slides.length} slides, ${config.autoplaySpeed}ms interval`);
        }
      }

      function stopAutoplay() {
        if (autoplayInterval) {
          clearInterval(autoplayInterval);
          autoplayInterval = null;
        }
      }

      function destroy() {
        isDestroyed = true;
        stopAutoplay();

        if (transitionTimeout) {
          clearTimeout(transitionTimeout);
          transitionTimeout = null;
        }

        if (window.location.search.includes('debug')) {
          console.log('Announcement slider destroyed and cleaned up');
        }
      }

      initSlides();
      startAutoplay();

      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.removedNodes.forEach((node) => {
              if (node === announcementBar || (node.contains && node.contains(announcementBar))) {
                destroy();
                observer.disconnect();
              }
            });
          }
        });
      });

      if (announcementBar.parentNode) {
        observer.observe(announcementBar.parentNode, { childList: true, subtree: true });
      }

      window.addEventListener('beforeunload', destroy);

      setTimeout(() => {
        if (announcementBar.swiper && !isDestroyed) {
          announcementBar.swiper.destroy(true, true);
          if (window.location.search.includes('debug')) {
            console.log('Theme slider destroyed');
          }
        }
      }, 100);

      window[`cleanupAnnouncement_${config.sectionId}`] = destroy;
    })();
  </script>
{%- endif -%}

{% schema %}
{
  "name": "Announcement Bar",
  "class": "shopify-section-announcement-bar-container",
  "settings": [
    {
      "type": "paragraph",
      "content": "Create multiple announcement slides with automatic rotation."
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Enable autoplay",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "label": "Autoplay speed (seconds)",
      "min": 2,
      "max": 10,
      "step": 1,
      "default": 3,
      "info": "How long each slide is displayed before switching to the next one"
    },
    {
      "type": "range",
      "id": "transition_speed",
      "label": "Transition speed (milliseconds)",
      "min": 300,
      "max": 1500,
      "step": 100,
      "default": 600,
      "info": "How fast the slide transition animation plays"
    }
  ],
  "blocks": [
    {
      "type": "announcement",
      "name": "Announcement",
      "settings": [
        {
          "type": "checkbox",
          "id": "show_icon",
          "label": "Show custom icon",
          "default": false,
          "info": "Enable this to display a custom SVG icon next to the text"
        },
        {
          "type": "html",
          "id": "custom_svg",
          "label": "Custom SVG Icon",
          "info": "Paste your custom SVG code here. Icon will only show if both 'Show custom icon' is enabled and this field contains SVG code. Example: <svg width='20' height='20'><path d='...' fill='white'/></svg>"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "RISK-FREE!",
          "info": "Main announcement text"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Subtitle",
          "default": "<p>120-day money-back guarantee</p>",
          "info": "Additional description text"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Announcement Bar",
      "settings": {
        "autoplay": true,
        "autoplay_speed": 3,
        "transition_speed": 600
      },
      "blocks": [
        {
          "type": "announcement",
          "settings": {
            "show_icon": false,
            "custom_svg": "",
            "title": "RISK-FREE!",
            "text": "<p>120-day money-back guarantee</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}
