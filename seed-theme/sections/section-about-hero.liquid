

{{ 'about-us-styles.css' | asset_url | stylesheet_tag }}

<div class="bg_page_about">
  <div class="about-hero-section">
    <div class="about-hero-content">
      {% if section.settings.title != blank %}
        <h1 class="about-hero-title">{{ section.settings.title }}</h1>
      {% endif %}

      <div class="about-hero-image-wrapper">
        {% if section.settings.image != blank %}
          <picture>
            <img
              src="{{ section.settings.image | image_url: width: 1180 }}"
              srcset="{% render 'image-srcset', image: section.settings.image %}"
              sizes="(min-width: 1300px) 1180px, 100vw"
              width="1180"
              height="360"
              alt="{{ section.settings.image.alt | default: section.settings.title | escape }}"
              loading="{% if section.index > 1 %}lazy{% else %}eager{% endif %}"
            >
          </picture>
        {% else %}
          <div class="about-hero-placeholder">
            <div class="meditation-illustration">
              <svg width="536" height="360" viewBox="0 0 536 360" fill="none" xmlns="http://www.w3.org/2000/svg">
                <!-- Background -->
                <rect width="536" height="360" fill="#B8D4F0" rx="8"/>

                <!-- Meditation platform -->
                <ellipse cx="268" cy="330" rx="220" ry="25" fill="#4ECDC4" opacity="0.3"/>
                <ellipse cx="268" cy="325" rx="200" ry="20" fill="#4ECDC4" opacity="0.5"/>
                <ellipse cx="268" cy="320" rx="180" ry="15" fill="#4ECDC4" opacity="0.7"/>

                <!-- Person sitting in meditation -->
                <circle cx="268" cy="150" r="30" fill="none" stroke="#666" stroke-width="2"/>
                <path d="M238 180 L238 220 L298 220 L298 180" fill="none" stroke="#666" stroke-width="2"/>
                <path d="M208 245 L238 220 L298 220 L328 245" fill="none" stroke="#666" stroke-width="2"/>
                <path d="M238 180 L208 200 L185 175" fill="none" stroke="#666" stroke-width="2"/>
                <path d="M298 180 L328 200 L351 175" fill="none" stroke="#666" stroke-width="2"/>

                <!-- Legs in lotus position -->
                <path d="M208 245 L230 270 L185 285" fill="none" stroke="#666" stroke-width="2"/>
                <path d="M328 245 L306 270 L351 285" fill="none" stroke="#666" stroke-width="2"/>

                <!-- Small product/logo -->
                <rect x="220" y="300" width="35" height="18" rx="4" fill="none" stroke="#4ECDC4" stroke-width="1"/>
                <text x="237.5" y="312" text-anchor="middle" font-size="10" fill="#4ECDC4">Upstep</text>
              </svg>
            </div>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>



{% schema %}
{
  "name": "About Hero Section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Hi, we're Upstep the foot-pain relief company."
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Hero Image"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "rgba(196, 196, 196, 0.1)"
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "label": "Desktop Spacing",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "label": "Mobile Spacing",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "px",
      "default": 60
    }
  ]
}
{% endschema %}
