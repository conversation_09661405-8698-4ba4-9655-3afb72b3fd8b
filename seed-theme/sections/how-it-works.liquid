{{ 'section-how-it-works.css' | asset_url | stylesheet_tag }}

<div class="how-it-works-section">
  <div class="page-width">
    {%- if section.settings.title != blank -%}
      <h2 class="how-it-works-title">{{ section.settings.title }}</h2>
    {%- endif -%}
    
    {%- if section.settings.shopify_video != blank or section.settings.external_video_url != blank -%}
      <div class="how-it-works-video-container">
        <div class="video-wrapper">
          {%- if section.settings.shopify_video != blank -%}
            {%- comment -%} Shopify hosted video {%- endcomment -%}
            {{ section.settings.shopify_video | video_tag: 
                image_size: '1920x1080',
                autoplay: section.settings.autoplay,
                loop: section.settings.loop,
                controls: section.settings.controls,
                muted: section.settings.muted,
                poster: section.settings.video_poster
            }}
          {%- elsif section.settings.external_video_url != blank -%}
            {%- comment -%} External video (YouTube/Vimeo) {%- endcomment -%}
            {%- liquid
                assign video_id = ''
                assign video_type = ''
                
                if section.settings.external_video_url contains 'youtube.com/watch?v='
                    assign video_id = section.settings.external_video_url | split: 'v=' | last | split: '&' | first
                    assign video_type = 'youtube'
                elsif section.settings.external_video_url contains 'youtu.be/'
                    assign video_id = section.settings.external_video_url | split: 'youtu.be/' | last | split: '?' | first
                    assign video_type = 'youtube'
                elsif section.settings.external_video_url contains 'vimeo.com/'
                    assign video_id = section.settings.external_video_url | split: 'vimeo.com/' | last | split: '?' | first
                    assign video_type = 'vimeo'
                endif
            -%}
            
            {%- if video_type == 'youtube' and video_id != blank -%}
              {%- liquid
                assign youtube_params = 'rel=0&modestbranding=1&autohide=1&showinfo=0'
                assign youtube_params = youtube_params | append: '&controls=' | append: section.settings.controls | default: 1
                assign youtube_params = youtube_params | append: '&autoplay=' | append: section.settings.autoplay | default: 0
                assign youtube_params = youtube_params | append: '&mute=' | append: section.settings.muted | default: 0

                if section.settings.loop
                  assign youtube_params = youtube_params | append: '&loop=1&playlist=' | append: video_id
                else
                  assign youtube_params = youtube_params | append: '&loop=0'
                endif
              -%}
              <iframe
                  src="https://www.youtube.com/embed/{{ video_id }}?{{ youtube_params }}"
                  frameborder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowfullscreen>
              </iframe>
            {%- elsif video_type == 'vimeo' and video_id != blank -%}
              <iframe 
                  src="https://player.vimeo.com/video/{{ video_id }}?autoplay={{ section.settings.autoplay | default: 0 }}&loop={{ section.settings.loop | default: 0 }}&muted={{ section.settings.muted | default: 0 }}"
                  frameborder="0" 
                  allow="autoplay; fullscreen; picture-in-picture" 
                  allowfullscreen>
              </iframe>
            {%- else -%}
              <p style="text-align: center; color: #666;">Invalid video URL. Please check the URL format.</p>
            {%- endif -%}
          {%- endif -%}
        </div>
      </div>
    {%- endif -%}
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // How It Works Video Enhancement
  const videoWrappers = document.querySelectorAll('.how-it-works-video-container .video-wrapper');
  
  videoWrappers.forEach(wrapper => {
    const video = wrapper.querySelector('video');
    const iframe = wrapper.querySelector('iframe');
    
    if (video) {
      // Add loading state
      wrapper.classList.add('loading');
      
      // Remove loading state when video is ready
      video.addEventListener('loadeddata', function() {
        wrapper.classList.remove('loading');
      });
      
      // Handle video load errors
      video.addEventListener('error', function() {
        wrapper.classList.remove('loading');
        console.warn('Video failed to load:', video.src);
      });
      
      // Intersection Observer for performance
      if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              // Video is in viewport, ensure it's loaded
              if (video.readyState === 0) {
                video.load();
              }
            } else {
              // Video is out of viewport, pause if playing
              if (!video.paused && !video.ended) {
                {% comment %} video.pause(); {% endcomment %}
              }
            }
          });
        }, {
          threshold: 0.5
        });
        
        observer.observe(wrapper);
      }
    } else if (iframe) {
      // Handle iframe videos (YouTube/Vimeo)
      wrapper.classList.add('loading');
      
      // Remove loading state when iframe loads
      iframe.addEventListener('load', function() {
        wrapper.classList.remove('loading');
      });
      
      // Handle iframe load errors
      iframe.addEventListener('error', function() {
        wrapper.classList.remove('loading');
        console.warn('Iframe failed to load:', iframe.src);
      });
    }
  });
});
</script>

{% schema %}
{
  "name": "How It Works Video",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "How it works"
    },
    {
      "type": "video",
      "id": "shopify_video",
      "label": "Shopify Hosted Video",
      "info": "Upload a video file to Shopify"
    },
    {
      "type": "url",
      "id": "external_video_url",
      "label": "External Video URL",
      "info": "YouTube or Vimeo URL (alternative to uploaded video)"
    },
    {
      "type": "image_picker",
      "id": "video_poster",
      "label": "Video Poster Image",
      "info": "Optional: Custom thumbnail image for the video"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay Video",
      "default": false,
      "info": "Note: Most browsers block autoplay with sound"
    },
    {
      "type": "checkbox",
      "id": "muted",
      "label": "Mute Video",
      "default": true,
      "info": "Required for autoplay to work in most browsers"
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop Video",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "controls",
      "label": "Show Video Controls",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "How It Works Video"
    }
  ]
}
{% endschema %}
