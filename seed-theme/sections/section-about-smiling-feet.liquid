{{ 'about-us-styles.css' | asset_url | stylesheet_tag }}

<div class="about-smiling-feet-section" 
     style="padding-top: {{ section.settings.spacing_desktop }}px; padding-bottom: {{ section.settings.spacing_desktop }}px;">
  <div class="about-smiling-feet-content">
    <div class="smiling-feet-layout">
      <!-- Left Column - Text Content -->
      <div class="smiling-feet-text-column">
        {% if section.settings.title != blank %}
          <h2 class="smiling-feet-title">{{ section.settings.title }}</h2>
        {% endif %}

        {% if section.settings.features != blank %}
          <div class="smiling-feet-features">
            {{ section.settings.features }}
          </div>
        {% endif %}
      </div>

      <!-- Right Column - Image -->
      <div class="smiling-feet-image-column">
        <!-- Decorative Clouds -->
        <div class="smiling-feet-clouds">
          <div class="cloud-top">
            <svg xmlns="http://www.w3.org/2000/svg" width="96" height="75" viewBox="0 0 96 75" fill="none">
              <mask id="cloud-top-a" mask-type="alpha" maskUnits="userSpaceOnUse" x="20" y="1" width="52" height="52">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M20.622 1H71.83v51.208H20.622z" fill="#fff"/>
              </mask>
              <g mask="url(#cloud-top-a)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M71.83 26.604c0 14.14-11.464 25.604-25.605 25.604S20.622 40.744 20.622 26.604C20.622 12.463 32.085 1 46.225 1S71.83 12.463 71.83 26.604" fill="#ECECEC"/>
              </g>
              <mask id="cloud-top-b" mask-type="alpha" maskUnits="userSpaceOnUse" x="60" y="18" width="36" height="36">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M60.99 18.539h34.97v34.969H60.99z" fill="#fff"/>
              </mask>
              <g mask="url(#cloud-top-b)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M95.96 36.023c0 9.657-7.828 17.485-17.484 17.485-9.658 0-17.486-7.828-17.486-17.485s7.828-17.484 17.486-17.484c9.656 0 17.484 7.828 17.484 17.484" fill="#ECECEC"/>
              </g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M34.183 20.353c0 7.49-6.071 13.561-13.561 13.561s-13.56-6.072-13.56-13.56c0-7.49 6.07-13.56 13.56-13.56s13.56 6.07 13.56 13.56" fill="#ECECEC"/>
              <mask id="cloud-top-c" mask-type="alpha" maskUnits="userSpaceOnUse" x="26" y="23" width="52" height="52">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M26.64 23.426h51.208v51.208H26.64z" fill="#fff"/>
              </mask>
              <g mask="url(#cloud-top-c)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M77.848 49.03c0 14.141-11.463 25.604-25.604 25.604S26.64 63.171 26.64 49.03s11.463-25.604 25.603-25.604S77.848 34.89 77.848 49.03" fill="#ECECEC"/>
              </g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M41.244 40.957c0 11.39-9.232 20.623-20.622 20.623S0 52.347 0 40.957s9.232-20.622 20.622-20.622 20.622 9.233 20.622 20.622" fill="#ECECEC"/>
            </svg>
          </div>
          <div class="cloud-bottom">
            <svg xmlns="http://www.w3.org/2000/svg" width="96" height="75" viewBox="0 0 96 75" fill="none">
              <mask id="cloud-bottom-a" mask-type="alpha" maskUnits="userSpaceOnUse" x="20" y="1" width="52" height="52">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M20.622 1H71.83v51.208H20.622z" fill="#fff"/>
              </mask>
              <g mask="url(#cloud-bottom-a)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M71.83 26.604c0 14.14-11.464 25.604-25.605 25.604S20.622 40.744 20.622 26.604C20.622 12.463 32.085 1 46.225 1S71.83 12.463 71.83 26.604" fill="#ECECEC"/>
              </g>
              <mask id="cloud-bottom-b" mask-type="alpha" maskUnits="userSpaceOnUse" x="60" y="18" width="36" height="36">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M60.99 18.539h34.97v34.969H60.99z" fill="#fff"/>
              </mask>
              <g mask="url(#cloud-bottom-b)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M95.96 36.023c0 9.657-7.828 17.485-17.484 17.485-9.658 0-17.486-7.828-17.486-17.485s7.828-17.484 17.486-17.484c9.656 0 17.484 7.828 17.484 17.484" fill="#ECECEC"/>
              </g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M34.183 20.353c0 7.49-6.071 13.561-13.561 13.561s-13.56-6.072-13.56-13.56c0-7.49 6.07-13.56 13.56-13.56s13.56 6.07 13.56 13.56" fill="#ECECEC"/>
              <mask id="cloud-bottom-c" mask-type="alpha" maskUnits="userSpaceOnUse" x="26" y="23" width="52" height="52">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M26.64 23.426h51.208v51.208H26.64z" fill="#fff"/>
              </mask>
              <g mask="url(#cloud-bottom-c)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M77.848 49.03c0 14.141-11.463 25.604-25.604 25.604S26.64 63.171 26.64 49.03s11.463-25.604 25.603-25.604S77.848 34.89 77.848 49.03" fill="#ECECEC"/>
              </g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M41.244 40.957c0 11.39-9.232 20.623-20.622 20.623S0 52.347 0 40.957s9.232-20.622 20.622-20.622 20.622 9.233 20.622 20.622" fill="#ECECEC"/>
            </svg>
          </div>
        </div>

        {% if section.settings.image != blank %}
          <div class="smiling-feet-image-wrapper">
            <img src="{{ section.settings.image | image_url: width: 600 }}"
                 alt="{{ section.settings.image.alt | default: section.settings.title }}"
                 width="600"
                 height="380"
                 loading="lazy">
          </div>
        {% else %}
          <div class="smiling-feet-image-placeholder">
            <div class="placeholder-content">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 19V5C21 3.9 20.1 3 19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 19 20.1 21 19ZM8.5 13.5L11 16.51L14.5 12L19 18H5L8.5 13.5Z" fill="#cccccc"/>
              </svg>
              <p>Add Image</p>
            </div>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<style>
/* Smiling Feet Section */
.about-smiling-feet-section {
  background-color: #fff;
  position: relative;
  z-index: 1;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.about-smiling-feet-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.smiling-feet-layout {
  display: flex;
  align-items: flex-start;
  gap: 100px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Left Column - Text */
.smiling-feet-text-column {
  flex: 1;
  max-width: 485px;
  padding-right: 20px;
}

.smiling-feet-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather_, serif;
  font-size: 36px;
  font-weight: 700;
  height: auto;
  letter-spacing: 0.12px;
  line-height: 50.4px;
  margin-bottom: 20px;
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0px;
  text-align: left;
  width: 100%;
  max-width: 485px;
}

.smiling-feet-features {
  font-family: Lato_, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  color: rgb(74, 74, 74);
}

.smiling-feet-features ul {
  margin: 0;
  padding-left: 20px;
}

.smiling-feet-features li {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: list-item;
  font-family: Lato_, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  list-style-type: disc;
  text-align: left;
  width: 100%;
  max-width: 445px;
  margin-bottom: 8px;
}

.smiling-feet-features li:last-child {
  margin-bottom: 0;
}

/* Right Column - Image */
.smiling-feet-image-column {
  flex: 1;
  max-width: 590px;
  position: relative;
}

.smiling-feet-image-wrapper {
  width: 100%;
  height: 379.891px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.smiling-feet-image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: opacity 0.3s ease;
}

.smiling-feet-image-placeholder {
  width: 100%;
  height: 379.891px;
  background-color: #f5f5f5;
  border: 2px dashed #cccccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.placeholder-content {
  text-align: center;
  color: #999999;
}

.placeholder-content p {
  margin: 10px 0 0 0;
  font-size: 14px;
  font-weight: 500;
}

/* Decorative Clouds */
.smiling-feet-clouds {
  position: absolute;
  top: -30px;
  left: -40px;
  right: -40px;
  bottom: -30px;
  pointer-events: none;
  z-index: 1;
}

.cloud-top {
  position: absolute;
  top: 0;
  right: 20px;
  opacity: 0.8;
}

.cloud-top svg {
  width: 70px;
  height: auto;
}

.cloud-bottom {
  position: absolute;
  bottom: 0;
  left: 30px;
  opacity: 0.8;
}

.cloud-bottom svg {
  width: 65px;
  height: auto;
}

/* Mobile Responsive */
@media only screen and (max-width: 768px) {
  .about-smiling-feet-section {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }

  .smiling-feet-layout {
    flex-direction: column-reverse;
    gap: 30px;
  }

  .smiling-feet-text-column {
    max-width: 100%;
    padding-right: 0;
  }

  .smiling-feet-image-column {
    max-width: 100%;
  }

  .smiling-feet-image-wrapper,
  .smiling-feet-image-placeholder {
    height: 320px;
  }

  .smiling-feet-image-wrapper img {
    object-fit: contain;
    height: 100%;
    width: 100%;
  }

  .smiling-feet-title {
    font-size: 28px;
    line-height: 39.2px;
    margin-bottom: 16px;
    max-width: 100%;
  }

  .smiling-feet-features {
    font-size: 14px;
    line-height: 20px;
  }

  .smiling-feet-features li {
    font-size: 14px;
    line-height: 20px;
    max-width: 100%;
  }

  .smiling-feet-clouds {
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
  }

  .cloud-top svg {
    width: 50px;
  }

  .cloud-bottom svg {
    width: 45px;
  }

  .cloud-top {
    right: 10px;
  }

  .cloud-bottom {
    left: 15px;
  }
}

@media only screen and (max-width: 480px) {
  .about-smiling-feet-content {
    padding: 0 15px;
  }

  .smiling-feet-title {
    font-size: 24px;
    line-height: 33.6px;
  }

  .smiling-feet-image-wrapper,
  .smiling-feet-image-placeholder {
    height: 233px;
  }

  .cloud-top svg {
    width: 40px;
  }

  .cloud-bottom svg {
    width: 35px;
  }
}
</style>

{% schema %}
{
  "name": "About Smiling Feet",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Over 200,000 smiling feet"
    },
    {
      "type": "richtext",
      "id": "features",
      "label": "Features List",
      "default": "<ul><li>Our team has over 35 years of experience making over 200,000 custom orthotics.</li><li>We use the best technology and materials available, over 56 material combinations.</li><li>We use research-based medicine, to define our approach to foot care and orthotic design.</li><li>Comprising our medical panel are clinicians with years of experience diagnosing and treating feet conditions.</li></ul>"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Section Image"
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Desktop Spacing",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "label": "Mobile Spacing",
      "default": 60
    }
  ]
}
{% endschema %}
