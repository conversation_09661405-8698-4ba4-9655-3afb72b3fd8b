{%- liquid
  assign current_variant = current_variant | default: product.selected_or_first_available_variant
  assign no_margin = no_margin | default: false
  assign extra_class = extra_class | default: false
  assign container = container | default: "p"
  assign shopify_attributes = shopify_attributes | default: false
  assign show_stock = settings.show_product_stock
  assign total_stock = 0
  if origin == 'productpage'
    assign bold = true
    if show_stock == false
      assign list_layout = true
    endif
  elsif origin == 'productitem'
    for variant in product.variants
      if variant.matched and variant.inventory_management == 'shopify'
        assign variant_stock = variant.inventory_quantity | at_least: 0
        assign total_stock = total_stock | plus: variant_stock
      endif
    endfor
  endif
  if total_stock == 0 and current_variant.inventory_management == 'shopify'
    assign total_stock = current_variant.inventory_quantity
  endif
-%}
{%- if settings.product_deliverytime_in_stock or settings.product_deliverytime_not_in_stock -%}
  {%- liquid
    assign deliverytime_in_stock_metafields_namespace = settings.product_deliverytime_in_stock | split: '.' | first
    assign deliverytime_in_stock_metafields_key = settings.product_deliverytime_in_stock | split: '.' | last
    assign deliverytime_not_in_stock_metafields_namespace = settings.product_deliverytime_not_in_stock | split: '.' | first
    assign deliverytime_not_in_stock_metafields_key = settings.product_deliverytime_not_in_stock | split: '.' | last
    assign deliverytime_in_stock = product.metafields[deliverytime_in_stock_metafields_namespace][deliverytime_in_stock_metafields_key]
    assign deliverytime_not_in_stock = product.metafields[deliverytime_not_in_stock_metafields_namespace][deliverytime_not_in_stock_metafields_key]
    unless deliverytime_in_stock
      assign deliverytime_in_stock = settings.default_product_deliverytime_in_stock
    endunless
    unless deliverytime_not_in_stock
      assign deliverytime_not_in_stock = settings.default_product_deliverytime_not_in_stock
    endunless

    assign deliverytime_info_metafields_namespace = settings.product_deliverytime_info | split: '.' | first
    assign deliverytime_info_metafields_key = settings.product_deliverytime_info | split: '.' | last
    assign deliverytime_info = product.metafields[deliverytime_info_metafields_namespace][deliverytime_info_metafields_key]
    unless deliverytime_info
      assign deliverytime_info = settings.default_product_deliverytime_info
    endunless

    if current_variant.inventory_management == 'shopify'
      assign inventory_tracking = true
      if show_stock
        assign show = true
      elsif total_stock > 0 and deliverytime_in_stock != empty
        assign show = true
      elsif total_stock <= 0 and deliverytime_not_in_stock != empty
        assign show = true
      endif
    elsif deliverytime_in_stock != empty and settings.show_deliverytime_always
      assign show = true
      assign inventory_tracking = false
      assign show_stock = false
    endif
    if total_stock > settings.show_product_stock_qty and settings.show_product_stock_always == false
      assign show_stock = false
    endif
  -%}
{%- endif -%}
{%- if show -%}
  {%- if extra_element -%}{{ extra_element }}{%- endif -%}
  {%- if total_stock > 0 or inventory_tracking == false -%}
    <{{ container }} class="stock {% if list_layout %}l4us{% endif %} overlay-valid {{ extra_class }} {% if no_margin %}m0{% endif %}" {% if shopify_attributes %}{{ shopify_attributes }}{% endif %}>
      {% if show_stock %}{{ 'product.form.in_stock' | t: amount: total_stock }}{% endif %}
      {%- if deliverytime_in_stock != empty -%}
        <span {% if bold %}class="strong"{% endif %}>
          {% unless list_layout %}<i aria-hidden="true" class="icon-check"></i>{% endunless %}
          {{ deliverytime_in_stock }}
          {%- if deliverytime_info != empty -%}&nbsp;<span class="s1tt{% if origin == 'productitem' %} mobile-hide{% endif %}"><span>{{ deliverytime_info | replace: '</p><p>', '<br>' | remove: '<p>' | remove: '</p>' }}</span></span>{%- endif -%}
        </span>
      {%- endif -%}
    </{{ container }}>
  {%- elsif inventory_tracking -%}
    <{{ container }} class="stock overlay-error {{ extra_class }} {% if no_margin %}m0{% endif %}" {% if shopify_attributes %}{{ shopify_attributes }}{% endif %}>
      {% if show_stock %}{{ 'product.form.not_in_stock' | t }}{%- if deliverytime_not_in_stock != empty -%},{% endif %} {% endif %}
      {%- if deliverytime_not_in_stock != empty -%}
        <span {% if bold %}class="strong"{% endif %}>
          {{ deliverytime_not_in_stock }}
          {%- if deliverytime_info != empty -%}&nbsp;<span class="s1tt{% if origin == 'productitem' %} mobile-hide{% endif %}"><span>{{ deliverytime_info | replace: '</p><p>', '<br>' | remove: '<p>' | remove: '</p>' }}</span></span>{%- endif -%}
        </span>
      {%- endif -%}
    </{{ container }}>
  {%- endif -%}
{%- endif -%}
