{%- assign icon = icon | default: false -%}
{% case icon %}
    {% when 'group' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 76 64" height="512" viewBox="0 0 76 64" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m38.13 33.16c-7.85 6.14-11.81 17.8-12.84 25.84h-25.29c0-7.15 3.42-20.9 11.61-28.54 6.844 6.835 17.737 7.417 25.27 1.35.388.476.805.927 1.25 1.35z" fill="ffffff"/><path d="m33 21c-.008 3.265.934 6.461 2.71 9.2-7.289 5.917-17.996 4.805-23.913-2.484s-4.805-17.996 2.484-23.913 17.996-4.805 23.913 2.484c.393.484.759.989 1.096 1.513-3.983 3.224-6.295 8.076-6.29 13.2z" fill="ffffff"/><path d="m50 39c-9.941 0-18-8.059-18-18s8.059-18 18-18 18 8.059 18 18c-.012 9.936-8.064 17.988-18 18zm0-34c-8.837 0-16 7.163-16 16s7.163 16 16 16 16-7.163 16-16c-.01-8.832-7.168-15.99-16-16z" fill="000000"/><path d="m25 64c-.552 0-1-.448-1-1 0-8.326 4.192-23.333 13.514-30.627.442-.332 1.068-.242 1.4.199.322.429.248 1.037-.168 1.376-8.791 6.88-12.746 21.13-12.746 29.052 0 .552-.448 1-1 1z" fill="000000"/><path d="m75 64c-.552 0-1-.448-1-1 0-7.922-3.955-22.172-12.746-29.052-.428-.349-.493-.979-.144-1.407.339-.416.946-.491 1.376-.168 9.322 7.294 13.514 22.301 13.514 30.627 0 .552-.448 1-1 1z" fill="000000"/></g></g></svg>' %}
    {% when 'notification' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 60 74.18" height="512" viewBox="0 0 60 74.18" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m34.946 6.635-2-.065.054-1.603c-.009-1.657-1.36-2.993-3.017-2.984-1.656.01-2.992 1.36-2.983 3.017l.052 1.57-2 .065-.052-1.602c-.009-2.761 2.222-5.007 4.983-5.016s5.008 2.222 5.017 4.983z" fill="000000"/><path d="m35.85 68.34c-.089 3.192-2.658 5.756-5.85 5.84-3.192-.084-5.761-2.648-5.85-5.84l.02-.53c1.81.12 3.75.19 5.83.19s4.02-.07 5.83-.19z" fill="ffffff"/><path d="m30 69c-22.716 0-29.5-8.028-29.777-8.37-.286-.353-.298-.854-.03-1.22 1.615-2.21 5.807-8.562 5.807-11.41v-19c0-13.907 10.094-24 24-24s24 10.093 24 24v19c0 2.848 4.191 9.2 5.806 11.41.268.366.256.867-.03 1.22-.276.342-7.06 8.37-29.776 8.37zm-27.693-9.109c1.931 1.721 9.436 7.109 27.693 7.109s25.755-5.384 27.691-7.11c-1.533-2.183-5.691-8.419-5.691-11.89v-19c0-12.748-9.252-22-22-22s-22 9.252-22 22v19c0 3.471-4.158 9.708-5.693 11.891z" fill="000000"/></g></g></svg>' %}
    {% when 'cloud_data' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 78.996 64.252" height="512" viewBox="0 0 78.996 64.252" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m62.744 56.252h-46.473c-8.181.121-15.145-5.928-16.171-14.046-.706-6.124 2.194-12.104 7.44-15.341-2.683-11.856 4.753-23.642 16.609-26.325.748-.169 1.505-.299 2.267-.39 9.42-1.155 18.509 3.898 22.5 12.508 6.121-3.019 13.53-.504 16.548 5.617 1.016 2.06 1.438 4.363 1.218 6.649 7.8 1.817 13.037 9.145 12.231 17.113-1.016 8.147-7.959 14.251-16.169 14.215zm-33.73-54.252c-.792.001-1.584.046-2.371.135-10.974 1.305-18.812 11.26-17.506 22.233.11.921.283 1.834.519 2.732.117.443-.082.908-.483 1.13-4.931 2.724-7.728 8.156-7.083 13.752.912 7.105 7.019 12.389 14.181 12.27h46.473c7.188.046 13.277-5.286 14.179-12.418.741-7.29-4.298-13.904-11.523-15.126-.545-.091-.913-.606-.822-1.15.001-.007.002-.013.003-.02.219-1.204.219-2.439 0-3.643-.878-4.81-4.995-8.355-9.882-8.508-2.019-.066-4.013.465-5.731 1.527-.471.288-1.087.14-1.375-.331-.028-.046-.052-.093-.072-.143-3.04-7.544-10.373-12.473-18.507-12.44z" fill="ffffff"/><g><path d="m27.637 63.48 7.652-8.927c.718-.839.62-2.102-.22-2.82-.362-.31-.823-.48-1.299-.48h-1.651c-.552 0-1-.448-1-1v-13c0-1.105-.895-2-2-2h-6c-1.105 0-2 .895-2 2v13c0 .552-.448 1-1 1h-1.652c-1.105 0-2 .896-1.999 2.001 0 .476.17.937.48 1.299l7.652 8.927c.719.839 1.981.936 2.82.217.078-.067.15-.139.217-.217z" fill="ffffff"/><path d="m51.6 36.023-7.652 8.927c-.718.839-.62 2.102.22 2.82.362.31.823.48 1.299.48h1.652c.552 0 1 .448 1 1v13c0 1.105.895 2 2 2h6c1.105 0 2-.895 2-2v-13c0-.552.448-1 1-1h1.651c1.105 0 2-.896 1.999-2.001 0-.476-.17-.937-.48-1.299l-7.652-8.927c-.719-.839-1.981-.936-2.82-.217-.078.067-.15.139-.217.217z" fill="ffffff"/></g></g></g></svg>' %}
    {% when 'verified' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 65.877 65.877" height="512" viewBox="0 0 65.877 65.877" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m32.938 65.877c-1.177.003-2.255-.659-2.784-1.71l-3.087-6.088c-.281-.553-.958-.774-1.511-.492-.035.018-.069.038-.103.059l-5.718 3.73c-1.445.941-3.379.534-4.32-.911-.362-.556-.538-1.212-.502-1.874l.371-6.817c.019-.318-.101-.629-.328-.853-.222-.23-.535-.35-.854-.328l-6.816.371c-1.177.077-2.294-.528-2.873-1.555-.592-1.018-.559-2.282.087-3.267l3.73-5.718c.339-.519.193-1.215-.327-1.554-.033-.022-.068-.042-.103-.06l-6.09-3.087c-1.538-.78-2.152-2.659-1.373-4.196.3-.592.781-1.073 1.373-1.373l6.09-3.087c.553-.281.773-.957.492-1.51-.018-.036-.038-.07-.06-.104l-3.732-5.718c-.646-.984-.68-2.249-.088-3.267.579-1.028 1.696-1.633 2.873-1.555l6.817.371c.318.017.628-.103.853-.328.227-.224.347-.535.328-.854l-.371-6.816c-.095-1.722 1.225-3.194 2.946-3.288.663-.036 1.321.14 1.877.502l5.718 3.73c.519.339 1.215.193 1.554-.327.022-.033.042-.068.06-.103l3.087-6.09c.78-1.538 2.659-2.152 4.196-1.373.592.3 1.073.781 1.373 1.373l3.087 6.09c.281.553.957.773 1.51.492.036-.018.07-.038.104-.06l5.718-3.732c1.445-.941 3.379-.534 4.32.911.362.556.538 1.212.502 1.874l-.371 6.815c-.032.621.445 1.149 1.066 1.181.039.002.077.002.116 0l6.816-.371c1.177-.078 2.294.527 2.873 1.555.592 1.018.558 2.282-.088 3.267l-3.73 5.718c-.338.521-.19 1.217.33 1.555.033.022.067.041.103.059l6.088 3.087c1.538.78 2.152 2.659 1.373 4.196-.3.592-.781 1.073-1.373 1.373l-6.088 3.09c-.553.281-.774.958-.492 1.511.018.035.038.069.059.103l3.73 5.718c.646.985.68 2.249.088 3.267-.58 1.027-1.696 1.631-2.873 1.555l-6.817-.371c-.621-.032-1.149.445-1.181 1.066-.002.039-.002.077 0 .116l.371 6.816c.094 1.722-1.226 3.193-2.948 3.287-.662.036-1.318-.14-1.874-.502l-5.718-3.73c-.521-.338-1.217-.19-1.555.33-.022.033-.041.067-.059.103l-3.087 6.088c-.53 1.051-1.608 1.713-2.785 1.71zm-6.871-10.412c.273-.002.544.032.808.1.855.23 1.573.811 1.976 1.6l3.087 6.088c.32.552 1.028.74 1.58.42.174-.101.319-.246.42-.42l3.087-6.088c.779-1.537 2.657-2.151 4.194-1.371.102.052.201.109.296.171l5.718 3.735c.519.339 1.215.192 1.554-.327.13-.199.193-.435.181-.673l-.368-6.816c-.048-.886.282-1.75.91-2.377.623-.632 1.49-.964 2.376-.91l6.817.371c.619.033 1.148-.443 1.181-1.062.013-.238-.051-.474-.181-.673l-3.729-5.717c-.943-1.442-.538-3.376.904-4.319.096-.062.194-.12.296-.171l6.088-3.087c.552-.281.772-.957.491-1.509-.108-.212-.28-.384-.491-.491l-6.088-3.087c-1.537-.779-2.151-2.657-1.371-4.194.052-.102.109-.201.171-.296l3.726-5.718c.237-.353.249-.81.032-1.175-.21-.367-.61-.584-1.032-.56l-6.816.371c-.886.053-1.753-.278-2.377-.91-.627-.627-.958-1.491-.91-2.376l.371-6.817c.033-.619-.443-1.148-1.062-1.181-.238-.013-.474.051-.673.181l-5.717 3.729c-1.442.943-3.376.538-4.319-.904-.062-.096-.12-.194-.171-.296l-3.087-6.092c-.32-.552-1.028-.74-1.58-.42-.174.101-.319.246-.42.42l-3.088 6.086c-.779 1.537-2.657 2.151-4.194 1.371-.102-.052-.201-.109-.296-.171l-5.717-3.723c-.519-.339-1.215-.192-1.554.327-.13.199-.193.435-.181.673l.371 6.816c.048.886-.282 1.75-.91 2.377-.623.633-1.49.965-2.376.91l-6.817-.371c-.422-.023-.821.193-1.032.56-.217.365-.205.822.032 1.175l3.729 5.717c.943 1.442.538 3.376-.904 4.319-.096.062-.194.12-.296.171l-6.092 3.087c-.552.281-.772.957-.491 1.509.108.212.28.384.491.491l6.086 3.088c1.537.779 2.151 2.657 1.371 4.194-.051.102-.109.2-.171.296l-3.723 5.717c-.339.519-.192 1.215.327 1.554.199.13.435.193.673.181l6.816-.371c.887-.055 1.754.277 2.377.91.627.627.958 1.491.91 2.376l-.371 6.817c-.033.619.443 1.148 1.062 1.181.238.013.474-.051.673-.181l5.717-3.729c.508-.33 1.1-.506 1.706-.506z" fill="000000"/><path d="m32.938 52.938c-11.046 0-20-8.954-20-20s8.954-20 20-20 20 8.954 20 20c-.012 11.041-8.959 19.988-20 20zm0-38c-9.941 0-18 8.059-18 18s8.059 18 18 18 18-8.059 18-18c-.012-9.936-8.064-17.988-18-18z" fill="000000"/><path d="m28.544 39.646c-.608 0-1.183-.276-1.563-.751l-4.166-5.458c-.69-.862-.551-2.121.311-2.812.862-.69 2.121-.551 2.812.312l3 4 12-8c.909-.629 2.155-.403 2.784.506s.403 2.155-.506 2.784l-13.534 9.063c-.334.232-.731.356-1.138.356z" fill="ffffff"/></g></g></svg>' %}
    {% when 'truck' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0" y="0" viewBox="0 0 53 53" style="enable-background:new 0 0 53 53" xml:space="preserve"><style>.st0{fill:none}.st2{fill="000000"}</style><path class="st0" d="M33.4 11H3.2c-1 0-1.9.8-1.9 1.9v20.8c0 1 .8 1.9 1.9 1.9h4.1c.8-2.2 2.9-3.8 5.3-3.8 2.5 0 4.6 1.6 5.3 3.8h17.4V12.9c0-1-.8-1.9-1.9-1.9zM43.5 33.1c-2.4 0-4.4 2-4.4 4.4 0 2.4 2 4.4 4.4 4.4 2.4 0 4.4-2 4.4-4.4 0-2.4-1.9-4.4-4.4-4.4z" fill="000000"/><path class="st0" d="M12.6 33.1c-2.4 0-4.4 2-4.4 4.4 0 2.4 2 4.4 4.4 4.4 2.4 0 4.4-2 4.4-4.4 0-2.4-1.9-4.4-4.4-4.4z" fill="000000"/><path d="m51.5 26.1-4.2-1.9-2-6.4C45 16.7 44 16 42.9 16h-6.3v20.2c0 .3-.3.6-.6.6h1.9c.3-2.8 2.7-5 5.6-5 2.9 0 5.3 2.2 5.6 5h1.3c1.4 0 2.5-1.1 2.5-2.5v-5.9c.1-.9-.5-1.8-1.4-2.3z" fill="ffffff"/><path class="st2" d="M36.6 36.3V12.9c0-1.7-1.4-3.2-3.2-3.2H3.2C1.4 9.8 0 11.2 0 12.9v20.8c0 1.7 1.4 3.2 3.2 3.2H7v.6c0 3.1 2.5 5.7 5.7 5.7 3.1 0 5.7-2.5 5.7-5.7v-.6H36c.3 0 .6-.3.6-.6zm-24 5.7c-2.4 0-4.4-2-4.4-4.4 0-2.4 2-4.4 4.4-4.4 2.4 0 4.4 2 4.4 4.4 0 2.4-1.9 4.4-4.4 4.4zm22.7-6.4H18c-.8-2.2-2.9-3.8-5.3-3.8-2.5 0-4.6 1.6-5.3 3.8H3.2c-1 0-1.9-.8-1.9-1.9V12.9c0-1 .8-1.9 1.9-1.9h30.3c1 0 1.9.8 1.9 1.9v22.7zM43.5 31.9c-2.9 0-5.3 2.2-5.6 5v.6c0 3.1 2.5 5.7 5.7 5.7 3.1 0 5.7-2.5 5.7-5.7v-.6c-.4-2.8-2.8-5-5.8-5zm0 10.1c-2.4 0-4.4-2-4.4-4.4 0-2.4 2-4.4 4.4-4.4 2.4 0 4.4 2 4.4 4.4C48 40 46 42 43.5 42z" fill="000000"/></svg>' %}
    {% when 'image_placeholder' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 54 66" height="512" viewBox="0 0 54 66" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m49 66h-44c-2.76-.003-4.997-2.24-5-5v-56c.003-2.76 2.24-4.997 5-5h31c.255.001.5.097.686.271l17 16c.2.19.314.453.314.729v44c-.003 2.76-2.24 4.997-5 5zm-44-64c-1.657 0-3 1.343-3 3v56c0 1.657 1.343 3 3 3h44c1.657 0 3-1.343 3-3v-43.568l-16.4-15.432z" fill="000000"/><path d="m53 18h-13c-2.76-.003-4.997-2.24-5-5v-12h2v12c0 1.657 1.343 3 3 3h13z" fill="000000"/><circle cx="15.47" cy="26" fill="ffffff" r="7"/><path d="m49 66h-43c-.552 0-1-.448-1-1 0-.198.058-.391.168-.555l11.732-17.595c1.532-2.298 4.636-2.918 6.934-1.387.031.021.062.042.093.064l4.749 3.325c.453.317 1.076.206 1.393-.246.028-.04.053-.083.075-.127l9.482-18.962c1.235-2.47 4.238-3.471 6.708-2.236.48.24.919.556 1.299.936l6.076 6.076c.187.188.291.442.291.707v26c-.003 2.76-2.24 4.997-5 5zm-41.131-2h41.131c1.657 0 3-1.343 3-3v-25.586l-5.783-5.783c-1.173-1.17-3.073-1.167-4.243.006-.226.227-.414.488-.557.774l-9.486 18.963c-.742 1.481-2.544 2.081-4.026 1.339-.13-.065-.255-.14-.374-.223l-4.749-3.324c-1.357-.95-3.228-.621-4.178.736-.013.019-.026.038-.039.057z" fill="000000"/></g></g></svg>' %}
    {% when 'help_call' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 71.409 70.881" height="512" viewBox="0 0 71.409 70.881" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m45.419 70.881c-7.055 0-16.846-3.554-29.416-16.124-22.503-22.505-15.903-36.12-11.953-40.98.157-.255.341-.492.55-.707l3.247-3.242c2.736-2.73 7.164-2.73 9.9 0l2.475 2.472c2.734 2.734 2.734 7.166.001 9.899 0 0 0 0-.001.001l-3 3c-1.975 1.975-2.859 5.085-2.424 8.531.57 4.518 3.37 9.405 8.1 14.131s9.647 7.558 14.202 8.166c3.445.458 6.658-.422 8.593-2.357l3-3c2.734-2.734 7.166-2.734 9.899-.001 0 0 0 0 .001.001l2.475 2.476c2.734 2.734 2.734 7.166.001 9.899 0 0 0 0-.001.001l-3.239 3.24c-.214.212-.452.398-.708.557-3.314 2.662-7.451 4.089-11.702 4.037zm-32.619-61.101c-1.326-.003-2.599.524-3.536 1.462l-3.244 3.24c-.115.119-.216.251-.3.394l-.082.116c-7.606 9.282-3.313 23.26 11.777 38.351 21.559 21.557 34.099 15.512 38.49 11.915l.112-.08c.145-.084.279-.185.4-.3l3.238-3.239c1.953-1.953 1.953-5.118 0-7.071l-2.475-2.476c-1.952-1.953-5.118-1.953-7.071-.001l-.001.001-3 3c-2.408 2.408-6.156 3.473-10.27 2.926-5-.667-10.31-3.687-15.358-8.735s-8.04-10.332-8.667-15.3c-.512-4.064.579-7.779 2.995-10.195l3-3c1.953-1.953 1.953-5.118 0-7.071l-2.476-2.476c-.936-.937-2.207-1.463-3.532-1.461z" fill="000000"/><path d="m50.692 0c-11.441 0-20.716 7.911-20.716 17.67.013 2.762.763 5.47 2.174 7.844l-2.868 12.429c-.152.655.257 1.31.912 1.461.243.056.497.037.728-.056l12.71-5.084c2.286.711 4.666 1.073 7.06 1.075 11.442 0 20.717-7.911 20.717-17.669s-9.275-17.67-20.717-17.67zm-10.035 20.9c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zm10 0c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zm10 0c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z" fill="ffffff"/></g></g></svg>' %}
    {% when 'filters' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 62 52" height="512" viewBox="0 0 62 52" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m7 52c-.552 0-1-.448-1-1v-50c0-.552.448-1 1-1s1 .448 1 1v50c0 .552-.448 1-1 1z" fill="000000"/><circle cx="7" cy="26" fill="ffffff" r="7"/><path d="m31 52c-.552 0-1-.448-1-1v-50c0-.552.448-1 1-1s1 .448 1 1v50c0 .552-.448 1-1 1z" fill="000000"/><circle cx="31" cy="26" fill="ffffff" r="7"/><path d="m55 52c-.552 0-1-.448-1-1v-50c0-.552.448-1 1-1s1 .448 1 1v50c0 .552-.448 1-1 1z" fill="000000"/><circle cx="55" cy="26" fill="ffffff" r="7"/></g></g></svg>' %}
    {% when 'shopping_bag' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 51.999 67" height="512" viewBox="0 0 51.999 67" width="512"><g><g><g><path d="m47.001 67h-42c-1.409 0-2.76-.599-3.706-1.642-.943-1.041-1.405-2.438-1.269-3.836l5.986-39.054c.246-2.524 2.385-4.468 4.97-4.468h30.04c2.574 0 4.707 1.93 4.972 4.494l5.977 38.975c.144 1.452-.319 2.85-1.263 3.891-.947 1.042-2.297 1.64-3.707 1.64zm-36.019-47c-1.551 0-2.834 1.168-2.985 2.718l-5.987 39.053c-.076.795.193 1.611.767 2.243.576.636 1.366.986 2.224.986h42c.858 0 1.648-.35 2.225-.984.573-.633.844-1.449.761-2.298l-5.974-38.946c-.003-.018-.005-.036-.007-.054-.151-1.55-1.434-2.718-2.984-2.718z" fill="000000"/></g><g><path d="m15.532 34c-2.757 0-5-2.243-5-5s2.243-5 5-5 5 2.243 5 5-2.244 5-5 5zm0-8c-1.654 0-3 1.346-3 3s1.346 3 3 3 3-1.346 3-3-1.346-3-3-3z" fill="000000"/></g><g><path d="m36.532 34c-2.757 0-5-2.243-5-5s2.243-5 5-5 5 2.243 5 5-2.244 5-5 5zm0-8c-1.654 0-3 1.346-3 3s1.346 3 3 3 3-1.346 3-3-1.346-3-3-3z" fill="000000"/></g></g><g><g><g><path d="m36.532 30c-.553 0-1-.447-1-1v-17.5c0-5.238-4.262-9.5-9.5-9.5s-9.5 4.262-9.5 9.5v17.5c0 .553-.447 1-1 1s-1-.447-1-1v-17.5c0-6.341 5.159-11.5 11.5-11.5s11.5 5.159 11.5 11.5v17.5c0 .553-.448 1-1 1z" fill="ffffff"/></g></g></g></g></svg>' %}
    {% when 'global_shipping' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 87.47 86" height="512" viewBox="0 0 87.47 86" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m44.47 86c-23.748 0-43-19.252-43-43s19.252-43 43-43 43 19.252 43 43c-.027 23.737-19.263 42.973-43 43zm0-84c-22.644 0-41 18.356-41 41s18.356 41 41 41 41-18.356 41-41c-.026-22.633-18.367-40.974-41-41z" fill="000000"/><path d="m56.09 84.37v-.99h-1.01l.5-.5-.211-.2c5.267-5.514 14.1-18 14.1-39.68 0-24.6-10.681-36.538-13.955-39.61l1.37-1.459c9.133 8.574 14.586 23.926 14.586 41.069 0 22.377-9.183 35.331-14.657 41.061-.189.197-.45.309-.723.309z" fill="000000"/><path d="m32.147 84.071c-5.481-5.729-14.677-18.684-14.677-41.071 0-24.4 11.213-37.606 14.649-41.074l1.421 1.408c-3.3 3.33-14.07 16.033-14.07 39.666 0 21.688 8.848 34.176 14.123 39.688z" fill="000000"/><path d="m44.47 27c-17.218 0-28.164-4.4-34.314-8.083l1.028-1.717c5.934 3.56 16.526 7.8 33.286 7.8s27.352-4.24 33.286-7.8l1.028 1.715c-6.15 3.685-17.096 8.085-34.314 8.085z" fill="000000"/><path d="m77.757 68.788c-5.948-3.553-16.551-7.788-33.287-7.788s-27.339 4.235-33.287 7.788l-1.025-1.717c6.163-3.682 17.119-8.071 34.312-8.071s28.149 4.389 34.313 8.071z" fill="000000"/><path d="m43.47 1h2v84h-2z" fill="000000"/><path d="m2.47 42h84v2h-84z" fill="000000"/><path d="m0 60s23.262 11.39 47.618 4.786c1.037-.295 1.65-1.363 1.382-2.407l-.68-2.72c-.268-1.072.384-2.157 1.455-2.425.344-.086.704-.079 1.045.02 4.336 1.285 12.2 3.568 19.482 5.446 1.069.279 1.709 1.372 1.43 2.44-.025.097-.058.192-.098.285-1.782 4.176-6.607 13.354-16.22 18.458-.98.519-2.194.145-2.713-.835-.153-.288-.233-.609-.233-.935v-1.449c.004-1.101-.886-1.997-1.988-2-.108 0-.216.008-.323.025-7.828 1.253-34.23 3.211-50.157-18.689z" fill="ffffff"/></g></g></svg>' %}
    {% when 'barcode' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 98 66" height="512" viewBox="0 0 98 66" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m22 62h-4c-1.105 0-2-.895-2-2v-54c0-1.105.895-2 2-2h4c1.105 0 2 .895 2 2v54c0 1.105-.895 2-2 2zm-4-56v54h4v-54z" fill="000000"/><path d="m53 62h-4c-1.105 0-2-.895-2-2v-54c0-1.105.895-2 2-2h4c1.105 0 2 .895 2 2v54c0 1.105-.895 2-2 2zm-4-56v54h4v-54z" fill="000000"/><path d="m84 62h-3c-1.105 0-2-.895-2-2v-54c0-1.105.895-2 2-2h3c1.105 0 2 .895 2 2v54c0 1.105-.895 2-2 2zm-3-56v54h3v-54z" fill="000000"/><path d="m65 62h-6c-1.105 0-2-.895-2-2v-54c0-1.105.895-2 2-2h6c1.105 0 2 .895 2 2v54c0 1.105-.895 2-2 2zm-6-56v54h6v-54z" fill="000000"/><g fill="ffffff"><path d="m89 4h4c.552 0 1 .448 1 1v56c0 .552-.448 1-1 1h-4c-.552 0-1-.448-1-1v-56c0-.552.448-1 1-1z" fill="ffffff"/><path d="m70 4h6c.552 0 1 .448 1 1v56c0 .552-.448 1-1 1h-6c-.552 0-1-.448-1-1v-56c0-.552.448-1 1-1z" fill="ffffff"/><path d="m5 4h8c.552 0 1 .448 1 1v56c0 .552-.448 1-1 1h-8c-.552 0-1-.448-1-1v-56c0-.552.448-1 1-1z" fill="ffffff"/><path d="m27 4h17c.552 0 1 .448 1 1v56c0 .552-.448 1-1 1h-17c-.552 0-1-.448-1-1v-56c0-.552.448-1 1-1z" fill="ffffff"/></g><path d="m97 66h-9c-.552 0-1-.448-1-1s.448-1 1-1h8v-8c0-.552.448-1 1-1s1 .448 1 1v9c0 .552-.448 1-1 1z" fill="000000"/><path d="m97 11c-.552 0-1-.448-1-1v-8h-8c-.552 0-1-.448-1-1s.448-1 1-1h9c.552 0 1 .448 1 1v9c0 .552-.448 1-1 1z" fill="000000"/><path d="m1 11c-.552 0-1-.448-1-1v-9c0-.552.448-1 1-1h9c.552 0 1 .448 1 1s-.448 1-1 1h-8v8c0 .552-.448 1-1 1z" fill="000000"/><path d="m10 66h-9c-.552 0-1-.448-1-1v-9c0-.552.448-1 1-1s1 .448 1 1v8h8c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/></g></g></svg>' %}
    {% when 'delivery_box_1' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" width="642" height="630" viewBox="0 0 642 630" fill="none"><path d="M257 214.429H385V561.857H257V214.429Z" fill="ffffff"/><path d="M385 214.429H257L284.429 68.1429H357.571L385 214.429Z" fill="ffffff"/><path d="M549.571 571H92.4286C77.2789 571 65 558.721 65 543.571V214.429C65 209.382 69.096 205.286 74.1429 205.286H567.857C572.904 205.286 577 209.382 577 214.429V543.571C577 558.721 564.721 571 549.571 571ZM83.2857 223.571V543.571C83.2857 548.618 87.3817 552.714 92.4286 552.714H549.571C554.618 552.714 558.714 548.618 558.714 543.571V223.571H83.2857Z" fill="000000"/><path d="M567.857 223.571H74.1429C69.096 223.571 65 219.475 65 214.429C65 213.496 65.1463 212.573 65.4206 211.686L107.13 78.2C110.687 66.744 121.311 58.9543 133.306 59H500.328C511.757 59.0366 521.969 66.1223 526.01 76.8103L576.424 211.21C578.198 215.937 575.811 221.213 571.085 222.986C570.051 223.37 568.963 223.571 567.857 223.571ZM86.5771 205.286H554.691L508.886 83.2194C507.551 79.6537 504.141 77.2857 500.328 77.2857H133.306C129.311 77.2857 125.773 79.8731 124.575 83.6857L86.5771 205.286Z" fill="000000"/></svg>' %}
    {% when 'delivery_box_2' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" width="642" height="630" viewBox="0 0 642 630" fill="none"><g clip-path="url(#clip0_2_19)"><path d="M551.008 521.452H138.105C124.421 521.452 113.33 510.361 113.33 496.677V199.387C113.33 194.829 117.03 191.129 121.588 191.129H567.524C572.082 191.129 575.782 194.829 575.782 199.387V496.677C575.782 510.361 564.691 521.452 551.008 521.452ZM129.846 207.645V496.677C129.846 501.236 133.546 504.935 138.105 504.935H551.008C555.566 504.935 559.266 501.236 559.266 496.677V207.645H129.846Z" fill="000000"/><path d="M567.524 207.645H121.588C117.03 207.645 113.33 203.946 113.33 199.387C113.33 198.545 113.462 197.711 113.71 196.91L151.375 76.3419C154.596 65.9946 164.192 58.9587 175.026 59H506.53C516.852 59.033 526.077 65.4248 529.727 75.0785L575.253 196.472C576.864 200.741 574.708 205.506 570.439 207.117C569.506 207.463 568.523 207.645 567.524 207.645ZM132.819 191.129H555.632L514.259 80.8756C513.054 77.655 509.973 75.5161 506.53 75.5161H175.026C171.418 75.5161 168.222 77.8532 167.14 81.2968L132.819 191.129Z" fill="000000"/><path d="M402.363 521.452H286.75C282.191 521.452 278.492 517.752 278.492 513.194V199.387C278.492 194.829 282.191 191.129 286.75 191.129H402.363C406.921 191.129 410.621 194.829 410.621 199.387V513.194C410.621 517.752 406.921 521.452 402.363 521.452ZM295.008 504.935H394.105V207.645H295.008V504.935Z" fill="000000"/><path d="M402.363 207.645H286.75C282.191 207.645 278.492 203.946 278.5 199.379C278.5 198.867 278.549 198.355 278.64 197.859L303.414 65.7303C304.149 61.8325 307.552 59 311.524 59H377.588C381.561 59 384.963 61.8325 385.698 65.7303L410.472 197.859C411.314 202.343 408.366 206.662 403.882 207.505C403.387 207.596 402.875 207.645 402.363 207.645V207.645ZM296.701 191.129H392.412L370.734 75.5161H318.378L296.701 191.129Z" fill="000000"/><path d="M133.728 344.457L70.1407 419.977C64.2692 426.955 65.161 437.376 72.1391 443.248C75.1037 445.742 78.8529 447.121 82.7342 447.129H96.7729C101.331 447.129 105.031 450.829 105.031 455.387V554.484C105.031 563.609 112.422 571 121.547 571H171.095C180.221 571 187.612 563.609 187.612 554.484V455.387C187.612 450.829 191.311 447.129 195.87 447.129H209.908C219.034 447.129 226.425 439.738 226.425 430.613C226.425 426.723 225.054 422.958 222.543 419.977L158.956 344.457C153.068 337.487 142.646 336.62 135.677 342.508C134.975 343.102 134.322 343.755 133.728 344.457V344.457Z" fill="ffffff"/></g><defs><clipPath id="clip0_2_19"><rect width="512" height="512" fill="white" transform="translate(65 59)"/></clipPath></defs></svg>' %}
    {% when 'statistic' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 77.099 75.296" height="512" viewBox="0 0 77.099 75.296" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m12.178 75.3h-7c-1.657 0-3-1.343-3-3v-23c0-1.657 1.343-3 3-3h7c1.657 0 3 1.343 3 3v23c0 1.657-1.343 3-3 3zm-7-27c-.552 0-1 .448-1 1v23c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-23c0-.552-.448-1-1-1z" fill="000000"/><path d="m66.178 75.3h-7c-1.657 0-3-1.343-3-3v-37c0-1.657 1.343-3 3-3h7c1.657 0 3 1.343 3 3v37c0 1.657-1.343 3-3 3zm-7-41c-.552 0-1 .448-1 1v37c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-37c0-.552-.448-1-1-1z" fill="000000"/><path d="m30.178 75.3h-7c-1.657 0-3-1.343-3-3v-32c0-1.657 1.343-3 3-3h7c1.657 0 3 1.343 3 3v32c0 1.657-1.343 3-3 3zm-7-36c-.552 0-1 .448-1 1v32c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-32c0-.552-.448-1-1-1z" fill="000000"/><path d="m48.178 75.3h-7c-1.657 0-3-1.343-3-3v-19c0-1.657 1.343-3 3-3h7c1.657 0 3 1.343 3 3v19c0 1.657-1.343 3-3 3zm-7-23c-.552 0-1 .448-1 1v19c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-19c0-.552-.448-1-1-1z" fill="000000"/><path d="m58.74 6.4.724.723c.781.781.781 2.047.001 2.828 0 0 0 0-.001.001l-14.688 14.691c-.781.781-2.047.781-2.828 0l-16.219-16.219c-.781-.781-2.047-.781-2.828-.001 0 0 0 0-.001.001l-22.314 22.315c-.781.781-.781 2.047 0 2.828l4.79 4.791c.781.781 2.047.781 2.828.001 0 0 0 0 .001-.001l14.695-14.696c.781-.781 2.047-.781 2.828-.001 0 0 0 0 .001.001l16.219 16.219c.781.781 2.047.781 2.828 0l22.307-22.306c.781-.781 2.047-.781 2.828 0l.784.784c.781.781 2.047.782 2.828.001.28-.279.47-.635.548-1.023l2.988-14.944c.216-1.083-.486-2.137-1.57-2.353-.259-.052-.525-.052-.783 0l-14.944 2.989c-1.083.219-1.783 1.274-1.565 2.356.078.384.267.737.543 1.015z" fill="ffffff"/></g></g></svg>' %}
    {% when 'review' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 83.352 69.153" height="512" viewBox="0 0 83.352 69.153" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m18.337 25.138c-.015-5.573 2.203-10.92 6.157-14.847.415-.415.853-.8 1.3-1.176l-4.706-8.115c-.554-.956-1.777-1.282-2.733-.728-.302.175-.553.426-.728.728l-4.992 8.617c-.283.489-.76.835-1.312.953l-9.74 2.084c-1.08.231-1.768 1.295-1.537 2.375.073.341.234.658.468.917l6.654 7.411c.377.421.559.981.5 1.543l-1.026 9.907c-.114 1.099.684 2.082 1.783 2.196.347.036.698-.019 1.017-.161l9.105-4.042c.377-.167.796-.213 1.2-.132-.928-2.402-1.406-4.955-1.41-7.53z" fill="ffffff"/><path d="m32.074 34.807-1.028-9.907c-.059-.562.123-1.122.5-1.543l6.654-7.411c.738-.822.67-2.086-.152-2.824-.26-.233-.576-.395-.918-.468l-9.737-2.084c-.025-.005-.047-.017-.071-.023-.467.386-.92.794-1.353 1.228-5.834 5.831-7.213 14.772-3.406 22.09l6.71 2.977c1.009.448 2.191-.007 2.64-1.016.142-.32.197-.671.161-1.019z" fill="ffffff"/><path d="m59.14 48.769c-.265 0-.52-.105-.707-.293l-5.657-5.656c-.39-.39-.39-1.024 0-1.414l2.829-2.829c.397-.375 1.017-.375 1.414 0l5.657 5.657c.39.39.39 1.024 0 1.414l-2.829 2.828c-.187.188-.442.294-.707.293zm-4.24-6.656 4.243 4.242 1.414-1.414-4.245-4.241z" fill="000000"/><path d="m76.111 69.153c-.796.001-1.559-.314-2.121-.877l-16.264-16.264c-1.172-1.171-1.172-3.071 0-4.243l4.244-4.242c1.171-1.171 3.07-1.171 4.242 0l16.263 16.263c1.169 1.173 1.169 3.07 0 4.243l-4.243 4.243c-.562.562-1.326.878-2.121.877zm-12.021-24.504c-.265 0-.519.105-.707.292l-4.243 4.242c-.391.39-.391 1.023-.001 1.414l.001.001 16.26 16.264c.391.39 1.023.39 1.414 0l4.242-4.242c.391-.39.391-1.023.001-1.414l-.001-.001-16.256-16.264c-.188-.188-.444-.293-.71-.292z" fill="000000"/><path d="m39.341 48.133c-12.703.006-23.005-10.287-23.011-22.989s10.287-23.005 22.989-23.011c6.107-.003 11.964 2.423 16.281 6.743 8.983 8.974 8.991 23.531.018 32.514-4.315 4.32-10.171 6.746-16.277 6.743zm0-43.982c-11.593.007-20.985 9.411-20.978 21.004s9.411 20.985 21.004 20.978 20.985-9.411 20.978-21.004c-.004-5.567-2.218-10.905-6.157-14.839-3.935-3.94-9.278-6.149-14.847-6.139zm0 39.889c-10.438-.007-18.894-8.475-18.887-18.913.008-10.439 8.476-18.894 18.914-18.887s18.894 8.475 18.887 18.913c-.004 5.01-1.996 9.814-5.54 13.356-3.545 3.549-8.358 5.54-13.374 5.531zm0-35.8c-9.337.004-16.904 7.576-16.9 16.914s7.576 16.904 16.914 16.9c9.337-.004 16.904-7.576 16.9-16.914-.002-4.483-1.784-8.782-4.955-11.951-3.165-3.182-7.471-4.966-11.959-4.953z" fill="000000"/></g></g></svg>' %}
    {% when 'email' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 92 38" height="512" viewBox="0 0 92 38" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m31.629 25.611h24.252v2h-24.252z" transform="matrix(.722 -.692 .692 .722 -6.25 37.665)" fill="000000"/><path d="m80.245 14.484h2v24.252h-2z" transform="matrix(.692 -.722 .722 .692 5.82 66.857)" fill="000000"/><path d="m88.85 1.86-23.52 23.52c-1.564 1.56-4.096 1.56-5.66 0l-23.52-23.52" fill="ffffff"/><path d="m29 9h-9c-.552 0-1-.448-1-1s.448-1 1-1h9c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/><path d="m13 9h-5c-.552 0-1-.448-1-1s.448-1 1-1h5c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/><path d="m29 20h-5c-.552 0-1-.448-1-1s.448-1 1-1h5c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/><path d="m18 20h-13c-.552 0-1-.448-1-1s.448-1 1-1h13c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/><path d="m10 32h-9c-.552 0-1-.448-1-1s.448-1 1-1h9c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/><path d="m29 32h-14c-.552 0-1-.448-1-1s.448-1 1-1h14c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/><path d="m87 38h-49c-2.76-.003-4.997-2.24-5-5v-28c.003-2.76 2.24-4.997 5-5h49c2.76.003 4.997 2.24 5 5v28c-.003 2.76-2.24 4.997-5 5zm-49-36c-1.657 0-3 1.343-3 3v28c0 1.657 1.343 3 3 3h49c1.657 0 3-1.343 3-3v-28c0-1.657-1.343-3-3-3z" fill="000000"/></g></g></svg>' %}
    {% when 'coin' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 81.793 81.793" height="512" viewBox="0 0 81.793 81.793" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m40.9 81.793c-22.588 0-40.9-18.312-40.9-40.9s18.312-40.9 40.9-40.9 40.9 18.312 40.9 40.9c-.024 22.579-18.321 40.876-40.9 40.9zm0-79.793c-21.484 0-38.9 17.416-38.9 38.9s17.416 38.9 38.9 38.9 38.9-17.416 38.9-38.9c-.022-21.475-17.425-38.878-38.9-38.9z" fill="000000"/><path d="m40.9 75.3c-18.999 0-34.4-15.401-34.4-34.4s15.401-34.4 34.4-34.4 34.4 15.401 34.4 34.4c-.023 18.989-15.411 34.377-34.4 34.4zm0-66.8c-17.894 0-32.4 14.506-32.4 32.4s14.506 32.4 32.4 32.4 32.4-14.506 32.4-32.4c-.018-17.887-14.513-32.384-32.4-32.405z" fill="000000"/><path d="m38.856 62.829v-4.829c-2.819-.047-5.571-.863-7.96-2.36l1.3-3.61c2.283 1.494 4.947 2.301 7.675 2.327 3.822 0 6.377-2.2 6.377-5.24 0-2.951-2.071-4.754-6.041-6.365-5.455-2.146-8.856-4.645-8.856-9.3.02-4.338 3.215-8.007 7.509-8.624v-5.86h3.061v5.69c4.056.1 6.254.967 7.784 1.868l-1.3 3.534c-2.042-1.223-4.378-1.866-6.758-1.86-4.114 0-5.672 2.471-5.672 4.64 0 2.743 1.97 4.152 6.626 6.1 5.5 2.254 8.3 5 8.3 9.759-.024 4.591-3.413 8.47-7.959 9.109v5.02z" fill="ffffff"/></g></g></svg>' %}
    {% when '24_hour_clock' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0" y="0" viewBox="0 0 53 53" style="enable-background:new 0 0 53 53" xml:space="preserve"><path class="st0" d="M40 25.1h-1.2l-3.6 5.2v.7H39v2.2h1V31h1.1v-.8H40v-5.1zm-1 2.3v2.7h-2.6l2-2.8c.2-.4.4-.7.7-1.2-.1.5-.1.9-.1 1.3zM26.9 10.7c-.3 0-.6.3-.6.6v14.9h-9.9c-.3 0-.6.3-.6.6s.3.6.6.6H27c.3 0 .6-.3.6-.6V11.3c-.1-.4-.4-.6-.7-.6zM30.7 32.2l.6-.6c1.7-1.6 2.8-2.8 2.8-4.3V27c-.1-1.3-1.2-2.2-2.5-2.1-.8 0-1.6.3-2.3.8l.3.8c.5-.4 1.1-.7 1.7-.7h.3c.8.1 1.4.8 1.3 1.6 0 1.2-.9 2.3-3 4.2l-.9.8v.7h5.2v-.9h-3.5z" fill="000000"/><path class="st0" d="M46.4 19.6C42.6 8.4 30.4 2.4 19.2 6.2 8 10.1 2.1 22.2 5.9 33.4c3 8.7 11.1 14.5 20.3 14.5 4.4 0 8.7-1.3 12.3-3.9l-.9-.9c-3.4 2.3-7.3 3.5-11.4 3.5C15 46.7 6 37.6 6 26.5 6 15.4 15 6.3 26.2 6.3c8.5 0 16.2 5.4 19 13.5v.1l.9.3.3-.6z" fill="000000"/><path class="st1" d="M40.2 18.9c.1-.3.5-.5.8-.4l.8.3c-2.8-5.7-8.7-9.7-15.6-9.7-9.6 0-17.4 7.8-17.4 17.4s7.8 17.4 17.4 17.4c9.6 0 17.4-7.8 17.4-17.4 0-2.2-.4-4.3-1.2-6.2l-1.9-.6c-.2-.1-.4-.5-.3-.8zm-12.7 7.9c0 .3-.3.6-.6.6H16.3c-.3 0-.6-.3-.6-.6s.3-.6.6-.6h9.9V11.3c0-.3.3-.6.6-.6s.6.3.6.6v15.5zm6.9 6.4h-5.2v-.7l.9-.8c2.1-2 3-3 3-4.2.1-.8-.5-1.5-1.3-1.6h-.3c-.6 0-1.2.3-1.7.7l-.3-.8c.6-.5 1.5-.8 2.3-.8 1.3-.1 2.4.8 2.5 2.1v.3c0 1.5-1.1 2.7-2.8 4.3l-.6.6h3.6v.9zm6.7-2.2H40v2.2h-1V31h-3.8v-.7l3.6-5.2H40v5h1.1v.9z" fill="ffffff"/><path class="st1" d="m38.3 27.3-2 2.8h2.6v-4c-.2.5-.4.9-.6 1.2z" fill="000000"/><path d="M49.2 16.1c-.3-.2-.7-.1-.8.2l-1.1 1.9C43.9 9.6 35.5 3.8 26.2 3.8 13.7 3.8 3.5 14 3.5 26.5s10.1 22.7 22.6 22.7c4.9 0 9.7-1.6 13.6-4.6l.1-.1c.2-.2.2-.6 0-.9L38 41.8c-.2-.2-.6-.2-.8-.1-3.2 2.4-7.1 3.6-11.1 3.6-8 0-15.2-5.1-17.8-12.6C4.8 23 10 12.1 19.8 8.6c9.6-3.4 20.1 1.4 23.8 10.8l-1.9-.6-.7-.3c-.3-.1-.7.1-.8.4-.1.3.1.7.4.8l1.9.6 3.7 1.2h.2c.2 0 .4-.1.5-.3l2.5-4.3c.2-.3.1-.6-.2-.8zm-3.1 4.1-.9-.3v-.1c-2.8-8.1-10.5-13.4-19-13.5C15 6.3 6 15.4 6 26.5s9 20.2 20.2 20.2c4.1 0 8.1-1.2 11.4-3.5l.9.9c-3.6 2.5-7.9 3.9-12.3 3.9C17 48 8.9 42.2 5.9 33.5 2.1 22.2 8 10.1 19.2 6.2c11.2-3.8 23.4 2.1 27.2 13.3l-.3.7z" fill="000000"/></svg>' %}
    {% when 'question' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 80.295 75.965" height="512" viewBox="0 0 80.295 75.965" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m36.854 39.489c-.443-2.89.461-5.824 2.455-7.962 1.593-1.9 2.665-3.379 2.665-5 .237-3.7-5.478-3.514-7.682-1.733l-1.226-3.7c4.934-3.164 14.361-2.108 14.158 4.67.2 5.186-6.4 7.894-5.855 12.832l.03.893zm-1.016 5.923c-.1-4.346 6.477-4.354 6.361 0 .086 4.319-6.436 4.328-6.361 0z" fill="ffffff"/><path d="m10.44 75.965c-1.657.001-3.001-1.341-3.002-2.998 0-.034.001-.068.002-.102l.642-19.082c-5.135-5.466-8.021-12.668-8.082-20.168 0-16.315 13.863-30.206 32.983-33.07.081-.028.165-.046.25-.053 2.29-.325 4.601-.49 6.914-.492 22.138 0 40.153 15.08 40.153 33.615 0 11.321-6.742 21.81-18.034 28.058-6.786 3.681-14.394 5.584-22.114 5.532-2.226-.007-4.449-.169-6.652-.485l-21.915 9.018c-.363.15-.752.227-1.145.227zm23.137-73.5c-.052.018-.106.031-.16.04-18.204 2.668-31.417 15.752-31.417 31.11.072 7.139 2.875 13.979 7.833 19.115.179.193.274.449.265.712l-.656 19.489c-.019.552.414 1.015.966 1.033.142.005.284-.021.415-.075l22.166-9.122c.166-.068.347-.091.524-.065 2.196.326 4.414.494 6.634.5 7.381.052 14.655-1.765 21.145-5.282 10.646-5.89 17-15.725 17-26.308.008-17.43-17.11-31.612-38.145-31.612-2.198.002-4.393.157-6.57.463z" fill="000000"/></g></g></svg>' %}
    {% when '24_7_call' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 72.68 74.589" height="512" viewBox="0 0 72.68 74.589" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m55.785 51.38c-.552.001-1.001-.445-1.002-.998-.001-.407.245-.775.622-.928 12.589-5.182 18.593-19.589 13.411-32.178-1.93-4.687-5.257-8.666-9.53-11.393-11.473-7.326-26.713-3.964-34.039 7.509 0 .001-.001.001-.001.002-.297.466-.915.602-1.381.305s-.602-.915-.305-1.381c7.92-12.405 24.397-16.041 36.802-8.121s16.041 24.398 8.121 36.803c-2.941 4.627-7.245 8.227-12.318 10.305-.121.049-.25.074-.38.075z" fill="000000"/><path d="m42.4 11.248c-.469-.001-.874-.327-.975-.784l-.763-3.454c-.119-.539.221-1.073.76-1.192s1.073.221 1.193.76l.763 3.455c.119.539-.222 1.073-.761 1.192-.071.015-.144.023-.217.023z" fill="000000"/><path d="m49.676 11.256c-.073 0-.146-.008-.218-.024-.539-.12-.879-.654-.759-1.193l.769-3.454c.12-.539.655-.879 1.194-.759s.879.655.759 1.194l-.77 3.453c-.102.457-.507.782-.975.783z" fill="000000"/><path d="m56.264 14.336c-.552-.001-.999-.449-.999-1.001 0-.22.073-.433.207-.608l2.157-2.8c.337-.438.965-.52 1.403-.183s.52.965.183 1.402l-2.158 2.805c-.19.244-.483.386-.793.385z" fill="000000"/><path d="m60.936 19.914c-.552.001-1.001-.446-1.002-.998-.001-.374.208-.718.54-.89l3.14-1.631c.49-.255 1.094-.063 1.349.427s.063 1.094-.427 1.349l-3.136 1.629c-.143.075-.302.114-.464.114z" fill="000000"/><path d="m62.811 26.942c-.552.012-1.009-.427-1.021-.979s.427-1.009.979-1.021l3.534-.151c.547-.011 1.006.41 1.042.956.024.552-.404 1.018-.956 1.042h-.001l-3.534.151z" fill="000000"/><path d="m64.808 35.462c-.131 0-.262-.026-.383-.077l-3.268-1.357c-.51-.212-.752-.797-.54-1.308.212-.51.797-.752 1.308-.539l3.267 1.358c.51.213.751.798.538 1.308-.155.372-.519.615-.922.615z" fill="000000"/><path d="m61.209 44.671c-.281 0-.549-.118-.739-.325l-2.387-2.611c-.373-.408-.345-1.04.063-1.413s1.04-.345 1.413.063l2.387 2.615c.373.407.345 1.04-.063 1.413-.184.168-.425.262-.674.262z" fill="000000"/><path d="m33.643 16.293c-.25 0-.492-.094-.676-.263l-2.606-2.393c-.407-.374-.434-1.006-.06-1.413s1.006-.434 1.413-.06l2.606 2.392c.407.373.434 1.006.061 1.413-.19.207-.457.324-.738.324z" fill="000000"/><path d="m54.883 54.021c-1.21-1.211-3.173-1.211-4.384-.001l-.001.001-5.047 5.047c-3.827 3.832-13.251 5.151-24.213-5.81s-9.5-20.249-5.673-24.076l5.047-5.047c1.211-1.21 1.211-3.173.001-4.384l-.001-.001-6.576-6.576c-1.21-1.211-3.173-1.211-4.384-.001l-.001.001-5.29 5.29c-.172.176-.322.372-.448.583-6.251 7.629-6.789 21.053 11.844 39.692s32.2 18.232 39.829 11.981c.211-.126.407-.276.583-.448l5.29-5.29c1.211-1.21 1.211-3.173.001-4.384l-.001-.001z" fill="ffffff"/><path d="m26.127 34.589v-1.2l1.236-1.15c2.452-2.268 3.609-3.518 3.622-4.895.071-.904-.604-1.694-1.508-1.765-.118-.009-.236-.006-.354.01-.817.039-1.599.343-2.226.868l-.578-1.4c.918-.702 2.044-1.078 3.2-1.069 1.702-.189 3.235 1.036 3.424 2.738.015.139.022.279.018.419 0 1.834-1.316 3.306-3.14 4.99l-.9.791v.032h4.269v1.63z" fill="000000"/><path d="m39.048 34.589v-2.65h-4.72v-1.284l4.256-6.49h2.329v6.279h1.343v1.5h-1.343v2.65zm0-4.145v-2.837c0-.589.026-1.189.062-1.8l-.056-.005c-.33.654-.6 1.187-.923 1.762l-1.907 2.853-.007.031z" fill="000000"/><path d="m42.731 35.231 3.899-11.631h1.385l-3.915 11.631z" fill="000000"/><path d="m55.838 24.165v1.263l-4.407 9.161h-2.075l4.394-8.761v-.028h-4.921v-1.635z" fill="000000"/></g></g></svg>' %}
    {% when 'speech_bubbles' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 68 53" height="512" viewBox="0 0 68 53" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m68 53-14-9h-31c-2.209 0-4-1.791-4-4v-7h27c2.206-.007 3.993-1.794 4-4v-17h11c2.206.007 3.993 1.794 4 4v19z" fill="ffffff"/><path d="m1 43c-.552 0-1-.448-1-1 0-.055.005-.11.014-.165l2.986-17.918v-18.917c.003-2.76 2.24-4.997 5-5h38c2.76.003 4.997 2.24 5 5v24c-.003 2.76-2.24 4.997-5 5h-30.706l-13.753 8.841c-.161.104-.349.159-.541.159zm7-41c-1.657 0-3 1.343-3 3v19c0 .055-.005.11-.014.165l-2.629 15.773 12.1-7.779c.162-.104.35-.159.543-.159h31c1.657 0 3-1.343 3-3v-24c0-1.657-1.343-3-3-3z" fill="000000"/><path d="m38 11h-21c-.552 0-1-.448-1-1s.448-1 1-1h21c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/><path d="m38 18h-21c-.552 0-1-.448-1-1s.448-1 1-1h21c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/><path d="m38 25h-21c-.552 0-1-.448-1-1s.448-1 1-1h21c.552 0 1 .448 1 1s-.448 1-1 1z" fill="000000"/></g></g></svg>' %}
    {% when 'coupon' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 94 58" height="512" viewBox="0 0 94 58" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m89 58h-61.343c-1.327.003-2.6-.524-3.536-1.465l-2.121-2.121-2.121 2.121c-.936.941-2.209 1.468-3.536 1.465h-11.343c-2.76-.003-4.997-2.24-5-5v-2.428c.015-1.252.824-2.356 2.013-2.747 1.56-.558 2.373-2.274 1.815-3.835-.303-.847-.969-1.513-1.815-1.815-1.189-.391-1.998-1.495-2.013-2.747v-4.856c.015-1.252.824-2.356 2.013-2.747 1.56-.558 2.373-2.274 1.815-3.835-.303-.847-.969-1.513-1.815-1.815-1.189-.391-1.998-1.495-2.013-2.747v-4.856c.015-1.252.824-2.356 2.013-2.747 1.191-.429 1.985-1.559 1.987-2.825.005-1.059-.553-2.041-1.466-2.579-1.557-.894-2.522-2.548-2.534-4.344v-1.077c.003-2.76 2.24-4.997 5-5h11.343c1.327-.003 2.6.524 3.536 1.465l2.121 2.121 2.121-2.121c.936-.941 2.209-1.468 3.536-1.465h61.343c2.76.003 4.997 2.24 5 5v2.428c-.015 1.252-.824 2.356-2.013 2.747-1.56.558-2.373 2.274-1.815 3.835.303.847.969 1.513 1.815 1.815 1.189.391 1.998 1.495 2.013 2.747v4.856c-.015 1.252-.824 2.356-2.013 2.747-1.56.558-2.373 2.274-1.815 3.835.303.847.969 1.513 1.815 1.815 1.189.391 1.998 1.495 2.013 2.747v4.856c-.015 1.252-.824 2.356-2.013 2.747-1.56.558-2.373 2.274-1.815 3.835.303.847.969 1.513 1.815 1.815 1.189.391 1.998 1.495 2.013 2.747v2.428c-.003 2.76-2.24 4.997-5 5zm-67-6c.265 0 .52.105.707.293l2.828 2.828c.562.565 1.326.881 2.122.879h61.343c1.657 0 3-1.343 3-3v-2.428c-.022-.405-.298-.751-.687-.864-2.6-.93-3.954-3.792-3.024-6.392.504-1.41 1.614-2.52 3.024-3.024.389-.113.665-.459.687-.864v-4.856c-.022-.405-.298-.751-.687-.864-2.6-.93-3.954-3.792-3.024-6.392.504-1.41 1.614-2.52 3.024-3.024.389-.113.665-.459.687-.864v-4.856c-.022-.405-.298-.751-.687-.864-2.6-.93-3.954-3.792-3.024-6.392.504-1.41 1.614-2.52 3.024-3.024.389-.113.665-.459.687-.864v-2.428c0-1.657-1.343-3-3-3h-61.343c-.796-.002-1.56.314-2.122.879l-2.828 2.828c-.39.39-1.024.39-1.414 0l-2.828-2.828c-.562-.565-1.326-.881-2.122-.879h-11.343c-1.657 0-3 1.343-3 3v1.077c.016 1.089.609 2.088 1.558 2.623 1.511.906 2.438 2.538 2.442 4.3-.003 2.11-1.327 3.993-3.313 4.708-.389.113-.665.459-.687.864v4.856c.022.405.298.751.687.864 2.6.93 3.954 3.792 3.024 6.392-.504 1.41-1.614 2.52-3.024 3.024-.389.113-.665.459-.687.864v4.856c.022.405.298.751.687.864 2.6.93 3.954 3.792 3.024 6.392-.504 1.41-1.614 2.52-3.024 3.024-.389.113-.665.459-.687.864v2.428c0 1.657 1.343 3 3 3h11.343c.796.002 1.56-.314 2.122-.879l2.828-2.828c.187-.188.442-.293.707-.293z" fill="000000"/><path d="m22 52c-.552 0-1-.448-1-1v-4c0-.552.448-1 1-1s1 .448 1 1v4c0 .552-.448 1-1 1zm0-10c-.552 0-1-.448-1-1v-4c0-.552.448-1 1-1s1 .448 1 1v4c0 .552-.448 1-1 1zm0-10c-.552 0-1-.448-1-1v-4c0-.552.448-1 1-1s1 .448 1 1v4c0 .552-.448 1-1 1zm0-10c-.552 0-1-.448-1-1v-4c0-.552.448-1 1-1s1 .448 1 1v4c0 .552-.448 1-1 1zm0-10c-.552 0-1-.448-1-1v-4c0-.552.448-1 1-1s1 .448 1 1v4c0 .552-.448 1-1 1z" fill="000000"/><path d="m54.081 23.573c0 5.329-3.389 8.122-7.08 8.122-3.865 0-6.926-2.906-6.939-7.7 0-4.556 2.786-8 7.13-8 4.401.005 6.889 3.215 6.889 7.578zm-9.571.291c-.026 2.673.886 4.568 2.619 4.568 1.677 0 2.5-1.707 2.5-4.595 0-2.593-.693-4.574-2.528-4.574-1.793 0-2.591 2.023-2.591 4.601zm2.931 18.136 14.459-26h3.149l-14.449 26zm25.029-7.823c0 5.327-3.389 8.141-7.08 8.141-3.865 0-6.926-2.927-6.938-7.718 0-4.554 2.785-8.015 7.129-8.015 4.401 0 6.889 3.232 6.889 7.592zm-9.571.291c-.027 2.673.894 4.588 2.62 4.588 1.677 0 2.5-1.728 2.5-4.614 0-2.594-.693-4.595-2.528-4.595-1.791 0-2.591 2.042-2.592 4.621z" fill="ffffff"/></g></g></svg>' %}
    {% when 'mobile_payment' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0" y="0" viewBox="0 0 53 53" style="enable-background:new 0 0 53 53" xml:space="preserve"><path class="st0" d="M16.8 49.2h19.5c1.1 0 1.9-.9 1.9-1.9v-6.5h-1.3V46c0 1.1-.9 1.9-1.9 1.9H18.1c-1.1 0-1.9-.9-1.9-1.9v-5.2h-1.3v6.5c-.1 1 .8 1.9 1.9 1.9z" fill="000000"/><path class="st0" d="M18.1 46.6H35c.4 0 .6-.3.6-.6v-5.2H17.4V46c0 .3.3.6.7.6zM34.9 10.3H18.1c-.4 0-.6.3-.6.6V18h18.2v-7.1c-.1-.3-.4-.6-.8-.6zM12.2 36.2H18c.4 0 .6-.3.6-.6 0-.4-.3-.6-.6-.6h-5.8c-.4 0-.6.3-.6.6s.3.6.6.6z" fill="000000"/><path class="st0" d="M36.2 3.8H16.8c-1.1 0-1.9.9-1.9 1.9V18h1.3v-7.1c0-1.1.9-1.9 1.9-1.9H35c1.1 0 1.9.9 1.9 1.9V18h1.3V5.7c0-1-.9-1.9-2-1.9zM29.1 7h-5.2c-.4 0-.6-.3-.6-.6 0-.4.3-.6.6-.6h5.2c.4 0 .6.3.6.6s-.2.6-.6.6z" fill="000000"/><path class="st1" d="M38.2 47.3c0 1.1-.9 1.9-1.9 1.9H16.8c-1.1 0-1.9-.9-1.9-1.9v-6.5h-1.4v6.5c0 1.8 1.5 3.2 3.2 3.2h19.5c1.8 0 3.2-1.5 3.2-3.2v-6.5h-1.3l.1 6.5zM14.8 5.7c0-1.1.9-1.9 1.9-1.9h19.5c1.1 0 1.9.9 1.9 1.9V18h1.4V5.7c0-1.7-1.5-3.2-3.3-3.2H16.8c-1.8 0-3.3 1.5-3.3 3.2V18h1.3V5.7z" fill="000000"/><path class="st1" d="M34.9 9H18.1c-1.1 0-1.9.9-1.9 1.9V18h1.3v-7.1c0-.4.3-.6.6-.6H35c.4 0 .6.3.6.6V18h1.3v-7.1c0-1-.9-1.9-2-1.9zM18.1 47.9H35c1.1 0 1.9-.9 1.9-1.9v-5.2h-1.3V46c0 .4-.3.6-.6.6H18.1c-.4 0-.6-.3-.6-.6v-5.2h-1.3V46c-.1 1 .8 1.9 1.9 1.9z" fill="000000"/><path class="st2" d="M7.7 38.2c0 1.4 1.2 2.6 2.6 2.6h32.4c1.4 0 2.6-1.2 2.6-2.6V24.6H7.7v13.6zm4.5-3.3H18c.4 0 .6.3.6.6 0 .4-.3.6-.6.6h-5.8c-.4 0-.6-.3-.6-.6s.3-.6.6-.6zM42.7 18.1H10.3c-1.4 0-2.6 1.2-2.6 2.6v2.6h37.6v-2.6c0-1.5-1.2-2.6-2.6-2.6z" fill="ffffff"/><path class="st1" d="M29.1 5.7h-5.2c-.4 0-.6.3-.6.6 0 .4.3.6.6.6h5.2c.4 0 .6-.3.6-.6s-.2-.6-.6-.6z" fill="ffffff"/></svg>' %}
    {% when 'calculator' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 58 74" height="512" viewBox="0 0 58 74" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m53 74h-48c-2.76-.003-4.997-2.24-5-5v-64c.003-2.76 2.24-4.997 5-5h48c2.76.003 4.997 2.24 5 5v64c-.003 2.76-2.24 4.997-5 5zm-48-72c-1.657 0-3 1.343-3 3v64c0 1.657 1.343 3 3 3h48c1.657 0 3-1.343 3-3v-64c0-1.657-1.343-3-3-3z" fill="000000"/><path d="m8 7h42c1.105 0 2 .895 2 2v13c0 1.105-.895 2-2 2h-42c-1.105 0-2-.895-2-2v-13c0-1.105.895-2 2-2z" fill="ffffff"/><path d="m16 40h-8c-1.657 0-3-1.343-3-3v-6c0-1.657 1.343-3 3-3h8c1.657 0 3 1.343 3 3v6c0 1.657-1.343 3-3 3zm-8-10c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h8c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1z" fill="000000"/><path d="m33 40h-8c-1.657 0-3-1.343-3-3v-6c0-1.657 1.343-3 3-3h8c1.657 0 3 1.343 3 3v6c0 1.657-1.343 3-3 3zm-8-10c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h8c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1z" fill="000000"/><path d="m50 40h-8c-1.657 0-3-1.343-3-3v-6c0-1.657 1.343-3 3-3h8c1.657 0 3 1.343 3 3v6c0 1.657-1.343 3-3 3zm-8-10c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h8c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1z" fill="000000"/><path d="m16 54h-8c-1.657 0-3-1.343-3-3v-6c0-1.657 1.343-3 3-3h8c1.657 0 3 1.343 3 3v6c0 1.657-1.343 3-3 3zm-8-10c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h8c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1z" fill="000000"/><path d="m33 54h-8c-1.657 0-3-1.343-3-3v-6c0-1.657 1.343-3 3-3h8c1.657 0 3 1.343 3 3v6c0 1.657-1.343 3-3 3zm-8-10c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h8c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1z" fill="000000"/><path d="m50 68h-8c-1.657 0-3-1.343-3-3v-20c0-1.657 1.343-3 3-3h8c1.657 0 3 1.343 3 3v20c0 1.657-1.343 3-3 3zm-8-24c-.552 0-1 .448-1 1v20c0 .552.448 1 1 1h8c.552 0 1-.448 1-1v-20c0-.552-.448-1-1-1z" fill="000000"/><path d="m16 68h-8c-1.657 0-3-1.343-3-3v-6c0-1.657 1.343-3 3-3h8c1.657 0 3 1.343 3 3v6c0 1.657-1.343 3-3 3zm-8-10c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h8c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1z" fill="000000"/><path d="m33 68h-8c-1.657 0-3-1.343-3-3v-6c0-1.657 1.343-3 3-3h8c1.657 0 3 1.343 3 3v6c0 1.657-1.343 3-3 3zm-8-10c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h8c.552 0 1-.448 1-1v-6c0-.552-.448-1-1-1z" fill="000000"/></g></g></svg>' %}
    {% when 'secure' %}
        {%- assign svg = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 54.088 59.815" height="512" viewBox="0 0 54.088 59.815" width="512"><g id="Layer_2_1_"><g id="linear_-_fill"><path d="m27.044 59.815c-.073 0-.146-.008-.218-.024-.268-.059-26.881-6.433-26.826-45.318.001-2.001 1.193-3.81 3.031-4.6l22.038-9.473c1.261-.539 2.687-.539 3.948 0l22.04 9.469c1.838.79 3.03 2.599 3.031 4.6.056 38.884-26.558 45.258-26.827 45.317-.071.018-.144.027-.217.029zm-.001-57.815c-.407 0-.81.082-1.185.242l-22.038 9.469c-1.105.475-1.82 1.563-1.82 2.765-.05 35.462 22.6 42.639 25.05 43.308 2.669-.688 25.089-7.544 25.038-43.308 0-1.202-.715-2.29-1.82-2.765l-22.04-9.47c-.375-.16-.778-.241-1.185-.241z" fill="000000"/><path d="m21.286 37.223c-.295 0-.575-.13-.765-.355l-6.242-7.408c-.356-.422-.302-1.054.121-1.41s1.054-.302 1.409.121l5.631 6.683 17-12.837c.442-.333 1.07-.245 1.403.197s.245 1.07-.197 1.403l-17.758 13.404c-.173.131-.385.202-.602.202z" fill="ffffff"/></g></g></svg>' %}
{% endcase %}
{{ svg | replace: 'fill="000000"', 'fill="var(--primary_text)"' | replace: 'fill="ffffff"', 'fill="var(--primary_bg_btn)"' }}