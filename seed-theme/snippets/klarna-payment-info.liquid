{%- assign current_variant = product.selected_or_first_available_variant -%}
{%- if current_variant -%}
  <div class="klarna-payment-section" id="klarna-section-{{ section_id }}">
    <div class="klarna-badge-container">
      <div class="klarna-logo-default" style="background: none; padding: 0; display: flex; align-items: center;">
        <svg aria-label="osm-klarna-title" width="70" height="30" viewBox="0 0 71.25 30" version="2.1" xmlns="http://www.w3.org/2000/svg" style="display: block;">
          <g clip-path="url(#a)">
            <path fill="#FFA8CD" d="M62.769 0H8.48A8.48 8.48 0 0 0 0 8.481V21.52A8.48 8.48 0 0 0 8.481 30H62.77a8.48 8.48 0 0 0 8.481-8.481V8.48A8.48 8.48 0 0 0 62.769 0"/>
            <path fill="#0B051D" d="M57.412 19.142c-1.244 0-2.213-1.029-2.213-2.278s.97-2.277 2.213-2.277 2.214 1.028 2.214 2.277-.97 2.278-2.214 2.278m-.621 2.406c1.06 0 2.414-.404 3.164-1.984l.073.037c-.329.863-.329 1.378-.329 1.506v.202h2.67v-8.89H59.7v.202c0 .129 0 .643.33 1.506l-.074.037c-.75-1.58-2.104-1.984-3.164-1.984-2.543 0-4.336 2.02-4.336 4.684s1.793 4.684 4.336 4.684m-8.983-9.368c-1.207 0-2.158.423-2.926 1.984l-.074-.037c.33-.863.33-1.377.33-1.506v-.202h-2.671v8.89h2.744v-4.684c0-1.23.713-2.002 1.866-2.002 1.152 0 1.719.662 1.719 1.984v4.702h2.744v-5.657c0-2.02-1.573-3.472-3.732-3.472m-9.31 1.984-.074-.037c.33-.863.33-1.377.33-1.506v-.202h-2.671v8.89h2.743l.019-4.28c0-1.248.658-2.002 1.738-2.002.292 0 .53.037.804.11V12.42c-1.207-.257-2.286.202-2.89 1.745m-8.727 4.978c-1.244 0-2.213-1.029-2.213-2.278s.97-2.277 2.213-2.277 2.214 1.028 2.214 2.277-.97 2.278-2.214 2.278m-.622 2.406c1.061 0 2.415-.404 3.165-1.984l.073.037c-.329.863-.329 1.378-.329 1.506v.202h2.67v-8.89h-2.67v.202c0 .129 0 .643.33 1.506l-.074.037c-.75-1.58-2.104-1.984-3.165-1.984-2.542 0-4.335 2.02-4.335 4.684s1.793 4.684 4.335 4.684m-8.158-.239h2.744V8.452H20.99zM18.978 8.452H16.18c0 2.296-1.409 4.353-3.55 5.822l-.84.588v-6.41H8.88v12.857h2.91v-6.373l4.81 6.373h3.55l-4.629-6.098c2.104-1.524 3.476-3.894 3.457-6.76"/>
          </g>
          <defs>
            <clipPath id="a">
              <path fill="#fff" d="M0 0h71.25v30H0z"/>
            </clipPath>
          </defs>
        </svg>
      </div>
      
      <div class="klarna-payment-text">
        <span class="klarna-payment-amount">4 interest-free payments of ${{ current_variant.price | divided_by: 4 | money_without_currency }}</span>
        <a href="#" class="klarna-learn-more" data-klarna-popup="{{ section_id }}">Learn more</a>
      </div>
    </div>
  </div>

  {%- comment -%} Klarna Popup {%- endcomment -%}
  <div class="klarna-popup-overlay" id="klarna-popup-{{ section_id }}">
    <div class="klarna-popup">
      <div class="klarna-popup-header">
        <div class="klarna-popup-logo" style="background: none; padding: 0; display: flex; align-items: center; justify-content: center;">
          <svg aria-label="osm-klarna-title" width="70" height="30" viewBox="0 0 71.25 30" version="2.1" xmlns="http://www.w3.org/2000/svg" style="display: block;">
            <g clip-path="url(#b)">
              <path fill="#FFA8CD" d="M62.769 0H8.48A8.48 8.48 0 0 0 0 8.481V21.52A8.48 8.48 0 0 0 8.481 30H62.77a8.48 8.48 0 0 0 8.481-8.481V8.48A8.48 8.48 0 0 0 62.769 0"/>
              <path fill="#0B051D" d="M57.412 19.142c-1.244 0-2.213-1.029-2.213-2.278s.97-2.277 2.213-2.277 2.214 1.028 2.214 2.277-.97 2.278-2.214 2.278m-.621 2.406c1.06 0 2.414-.404 3.164-1.984l.073.037c-.329.863-.329 1.378-.329 1.506v.202h2.67v-8.89H59.7v.202c0 .129 0 .643.33 1.506l-.074.037c-.75-1.58-2.104-1.984-3.164-1.984-2.543 0-4.336 2.02-4.336 4.684s1.793 4.684 4.336 4.684m-8.983-9.368c-1.207 0-2.158.423-2.926 1.984l-.074-.037c.33-.863.33-1.377.33-1.506v-.202h-2.671v8.89h2.744v-4.684c0-1.23.713-2.002 1.866-2.002 1.152 0 1.719.662 1.719 1.984v4.702h2.744v-5.657c0-2.02-1.573-3.472-3.732-3.472m-9.31 1.984-.074-.037c.33-.863.33-1.377.33-1.506v-.202h-2.671v8.89h2.743l.019-4.28c0-1.248.658-2.002 1.738-2.002.292 0 .53.037.804.11V12.42c-1.207-.257-2.286.202-2.89 1.745m-8.727 4.978c-1.244 0-2.213-1.029-2.213-2.278s.97-2.277 2.213-2.277 2.214 1.028 2.214 2.277-.97 2.278-2.214 2.278m-.622 2.406c1.061 0 2.415-.404 3.165-1.984l.073.037c-.329.863-.329 1.378-.329 1.506v.202h2.67v-8.89h-2.67v.202c0 .129 0 .643.33 1.506l-.074.037c-.75-1.58-2.104-1.984-3.165-1.984-2.542 0-4.335 2.02-4.335 4.684s1.793 4.684 4.335 4.684m-8.158-.239h2.744V8.452H20.99zM18.978 8.452H16.18c0 2.296-1.409 4.353-3.55 5.822l-.84.588v-6.41H8.88v12.857h2.91v-6.373l4.81 6.373h3.55l-4.629-6.098c2.104-1.524 3.476-3.894 3.457-6.76"/>
            </g>
            <defs>
              <clipPath id="b">
                <path fill="#fff" d="M0 0h71.25v30H0z"/>
              </clipPath>
            </defs>
          </svg>
        </div>
        <button class="klarna-popup-close" data-klarna-close="{{ section_id }}">&times;</button>
      </div>
      
      <div class="klarna-popup-content">
        <h2 class="klarna-popup-main-title">Buy now. Pay with Klarna at your own pace.</h2>
        <p class="klarna-popup-subtitle">Get what you love, choose how you pay.</p>
        
        <div class="klarna-payment-options">
          <div class="klarna-option klarna-pay-full">
            <div class="klarna-option-price">${{ current_variant.price | money_without_currency }}</div>
            <div class="klarna-option-label">Pay now</div>
            <div class="klarna-option-badge">Pay in full</div>
          </div>
          
          <div class="klarna-option klarna-pay-installments">
            <div class="klarna-option-price">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
            <div class="klarna-option-label">Every 14 days</div>
            <div class="klarna-option-badge">Pay in 4</div>
            
            <div class="klarna-installment-schedule">
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-1"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">Today</div>
                </div>
              </div>
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-2"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">In 2 weeks</div>
                </div>
              </div>
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-3"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">In 4 weeks</div>
                </div>
              </div>
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-4"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">In 6 weeks</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="klarna-summary">
          <div class="klarna-summary-item">
            <span class="klarna-summary-label">APR</span>
            <span class="klarna-summary-value">0%</span>
          </div>
          <div class="klarna-summary-item">
            <span class="klarna-summary-label">Total interest</span>
            <span class="klarna-summary-value">Free</span>
          </div>
          <div class="klarna-summary-item">
            <span class="klarna-summary-label">Total</span>
            <span class="klarna-summary-value">${{ current_variant.price | money_without_currency }}</span>
          </div>
        </div>
        
        <p class="klarna-disclaimer">For Pay in 4, CA resident loans made or arranged pursuant to California financing law license. NMLS # 1353190.</p>
        
        <div class="klarna-how-it-works">
          <h3>How it works</h3>
          <div class="klarna-step">
            <div class="klarna-step-number">1</div>
            <div class="klarna-step-content">
              <h4>At checkout select Klarna</h4>
            </div>
          </div>
          <div class="klarna-step">
            <div class="klarna-step-number">2</div>
            <div class="klarna-step-content">
              <h4>Choose your payment plan</h4>
              <p>Different payment plans may be shown depending on the purchase amount and credit score.</p>
            </div>
          </div>
          <div class="klarna-step">
            <div class="klarna-step-number">3</div>
            <div class="klarna-step-content">
              <h4>Complete your checkout</h4>
              <p>The amount will be charged based on the payment plan you chose.</p>
            </div>
          </div>
        </div>
        
        <button class="klarna-got-it-btn" data-klarna-close="{{ section_id }}">Got it</button>
      </div>
    </div>
  </div>

  <script>
  document.addEventListener('DOMContentLoaded', function() {
    const sectionId = '{{ section_id }}';

    // Open popup
    const learnMoreBtn = document.querySelector('[data-klarna-popup="' + sectionId + '"]');
    const popup = document.getElementById('klarna-popup-' + sectionId);

    if (learnMoreBtn && popup) {
      learnMoreBtn.addEventListener('click', function(e) {
        e.preventDefault();
        popup.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        document.body.classList.add('klarna-popup-open');
      });
    }

    // Close popup function
    function closePopup() {
      if (popup) {
        popup.style.display = 'none';
        document.body.style.overflow = '';
        document.body.classList.remove('klarna-popup-open');
      }
    }

    // Close popup
    const closeButtons = document.querySelectorAll('[data-klarna-close="' + sectionId + '"]');
    closeButtons.forEach(function(btn) {
      btn.addEventListener('click', closePopup);
    });

    // Close on overlay click
    if (popup) {
      popup.addEventListener('click', function(e) {
        if (e.target === popup) {
          closePopup();
        }
      });
    }

    // Update prices when variant changes
    const productForm = document.querySelector('#product-form-' + sectionId);
    if (productForm) {
      productForm.addEventListener('change', function(e) {
        if (e.target.name === 'id' || e.target.name.includes('options[')) {
          updateKlarnaPrices(sectionId);
        }
      });
    }

    function updateKlarnaPrices(sectionId) {
      const productForm = document.querySelector('#product-form-' + sectionId);
      if (!productForm) return;

      const variantIdInput = productForm.querySelector('[name="id"]');
      if (!variantIdInput) return;

      const variantId = variantIdInput.value;
      const productDataElement = document.querySelector('#ProductVariantsJSON-{{ product.id }}');

      if (!productDataElement) return;

      try {
        const productData = JSON.parse(productDataElement.textContent || '{}');
        const currentVariant = productData.variants?.find(variant => variant.id == variantId);

        if (currentVariant) {
          const installmentAmount = (currentVariant.price / 4 / 100).toFixed(2);
          const fullAmount = (currentVariant.price / 100).toFixed(2);

          // Update badge
          const badgeAmount = document.querySelector('#klarna-section-' + sectionId + ' .klarna-payment-amount');
          if (badgeAmount) {
            badgeAmount.textContent = '4 interest-free payments of $' + installmentAmount;
          }

          // Update popup amounts
          const popupAmounts = document.querySelectorAll('#klarna-popup-' + sectionId + ' .klarna-option-price');
          if (popupAmounts[0]) popupAmounts[0].textContent = '$' + fullAmount;
          if (popupAmounts[1]) popupAmounts[1].textContent = '$' + installmentAmount;

          // Update installment amounts
          const installmentAmounts = document.querySelectorAll('#klarna-popup-' + sectionId + ' .klarna-amount');
          installmentAmounts.forEach(function(amount) {
            amount.textContent = '$' + installmentAmount;
          });

          // Update total
          const totalAmount = document.querySelector('#klarna-popup-' + sectionId + ' .klarna-summary-item:last-child .klarna-summary-value');
          if (totalAmount) {
            totalAmount.textContent = '$' + fullAmount;
          }
        }
      } catch (error) {
        console.error('Error updating Klarna prices:', error);
      }
    }

    // Handle escape key to close popup
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        if (popup && popup.style.display === 'flex') {
          closePopup();
        }
      }
    });
  });
  </script>
{%- endif -%}
