/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "breadcrumbs": {
      "type": "breadcrumbs",
      "settings": {
        "hide_mobile": false
      }
    },
    "165529092205bba5ad": {
      "type": "section-slideshow",
      "blocks": {
        "1655290922a52241b1-0": {
          "type": "slide",
          "settings": {
            "layout": "background",
            "overlay_opacity": 20,
            "color_palette": "scheme-2",
            "text_position": "text-center",
            "title": "<h2>{{ collection.title }}</h2>",
            "title_underline_style": "none",
            "text": "{{ collection.description }}",
            "show_first_link": false,
            "first_link_text": "Button",
            "first_link_url": "/",
            "first_button_style": "primary plain",
            "show_second_link": false,
            "second_link_text": "Button",
            "second_link_url": "",
            "second_button_style": "secondary plain"
          }
        }
      },
      "block_order": [
        "1655290922a52241b1-0"
      ],
      "settings": {
        "height": "32/9",
        "width": "wide",
        "autoplay": false,
        "autoplay_seconds": 3,
        "enable_controls": false,
        "height_mobile": "xs",
        "spacing_desktop": 50,
        "spacing_mobile": 30
      }
    },
    "main-collection": {
      "type": "main-collection",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {
            "title_size": "h2",
            "text_alignment": "left",
            "title": ""
          }
        },
        "sorting": {
          "type": "sorting",
          "settings": {}
        },
        "product_grid": {
          "type": "product_grid",
          "settings": {}
        },
        "423fb7f6-2c97-4420-8211-d45378349d2a": {
          "type": "description",
          "settings": {
            "text_alignment": "left",
            "enable_read_more": false,
            "text": ""
          }
        }
      },
      "block_order": [
        "title",
        "sorting",
        "product_grid",
        "423fb7f6-2c97-4420-8211-d45378349d2a"
      ],
      "settings": {
        "pagination_qty": 48,
        "pagination_type": "pages",
        "show_amount_of_products_in_collection": true,
        "productview_type": "grid",
        "products_per_row": 3,
        "show_collection_image": false,
        "enable_filters": true,
        "enable_menu": true,
        "enable_collections": true,
        "filters_menu_alignment": "left",
        "collections_menu": [],
        "filter_sticky": true,
        "filters_show_more_limit": 7,
        "image_swatches_size": "xs",
        "image_swatches_fill_images": true,
        "image_swatches_show_in_circle": true,
        "enable_quick_buy_desktop": true,
        "enable_quick_buy_mobile": false,
        "enable_quick_buy_drawer": true,
        "enable_quick_buy_qty_selector": true,
        "enable_color_picker": false,
        "enable_quick_buy_compact": false,
        "products_per_row_mobile": "1",
        "filter_mobile_sticky": true
      }
    },
    "16552917198b5c7149": {
      "type": "section-promo-gallery",
      "blocks": {
        "1655291718e99480f0-0": {
          "type": "image",
          "settings": {
            "overlay_opacity_img": 0,
            "color_palette": "scheme-1",
            "overlay_opacity": 0,
            "text_position": "text-center",
            "title": "Houseplants",
            "title_underline_style": "none",
            "text": "",
            "show_link": true,
            "link_text": "Discover now",
            "link_url": "/",
            "button_style": "primary link"
          }
        },
        "1655291718e99480f0-1": {
          "type": "image",
          "settings": {
            "overlay_opacity_img": 0,
            "color_palette": "scheme-1",
            "overlay_opacity": 0,
            "text_position": "text-center",
            "title": "Accessories and Gifts",
            "title_underline_style": "none",
            "text": "",
            "show_link": true,
            "link_text": "Discover now",
            "link_url": "/",
            "button_style": "primary link"
          }
        },
        "1655291718e99480f0-2": {
          "type": "image",
          "settings": {
            "overlay_opacity_img": 0,
            "color_palette": "scheme-1",
            "overlay_opacity": 0,
            "text_position": "text-center",
            "title": "Toolkits",
            "title_underline_style": "none",
            "text": "",
            "show_link": true,
            "link_text": "Discover now",
            "link_url": "/",
            "button_style": "primary link"
          }
        }
      },
      "block_order": [
        "1655291718e99480f0-0",
        "1655291718e99480f0-1",
        "1655291718e99480f0-2"
      ],
      "settings": {
        "items_width": 3,
        "width": "boxed",
        "height": "16/9",
        "space_between": 16,
        "image_zoom": true,
        "shadow": false,
        "title_size_blocks": "h3",
        "layout_mobile": "rows",
        "height_mobile": "16/9",
        "spacing_desktop": 50,
        "spacing_mobile": 30
      }
    }
  },
  "order": [
    "breadcrumbs",
    "165529092205bba5ad",
    "main-collection",
    "16552917198b5c7149"
  ]
}
