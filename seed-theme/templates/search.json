/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "breadcrumbs": {
      "type": "breadcrumbs",
      "settings": {
        "hide_mobile": false
      }
    },
    "main-search": {
      "type": "main-search",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {
            "text_alignment": "left"
          }
        },
        "sorting": {
          "type": "sorting",
          "settings": {}
        },
        "results": {
          "type": "results",
          "settings": {}
        }
      },
      "block_order": [
        "title",
        "sorting",
        "results"
      ],
      "settings": {
        "pagination_qty": 12,
        "show_amount_of_search_results": true,
        "productview_type": "grid",
        "products_per_row": 3,
        "enable_filters": true,
        "enable_menu": true,
        "filters_menu_alignment": "left",
        "filter_sticky": true,
        "filters_show_more_limit": 7,
        "enable_quick_buy_desktop": true,
        "enable_quick_buy_mobile": true,
        "enable_quick_buy_qty_selector": false,
        "products_per_row_mobile": "2",
        "filter_mobile_sticky": false
      }
    }
  },
  "order": [
    "breadcrumbs",
    "main-search"
  ]
}
