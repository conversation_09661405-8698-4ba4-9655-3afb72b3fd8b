@keyframes spin { 0% { transform: none; } 100% { transform: rotate(360deg); } }

@media only screen and (max-width: 1000px) {

.f8fl .f8fl-toggle:before { display: block; overflow: visible; position: absolute; left: 0; right: 0; top: 50%; bottom: auto; margin: -10px 0 0; box-shadow: none; border-radius: 0; border-width: 0; background: none; font-weight: 400; font-family: i; line-height: 20px; text-align: center; text-indent: 0; letter-spacing: normal; }

.js .f8fl { margin-bottom: 0; padding-bottom: 0; border-bottom-width: 0; --margin_off: var(--rpn); }
	.js .m6pn .f8fl { --margin_off: var(--rpn); }
	.js .f8fl > .f8fl-toggle, .js .f8fl > .close { display: block; overflow: hidden; position: absolute; right: 0; top: 0; z-index: 999; width: 48px; height: 48px; color: var(--custom_top_main_fg); font-size: var(--main_fz_small); text-align: left; text-indent: -3000em; direction: ltr; }
		.js .f8fl > .f8fl-toggle:before, .js .f8fl > .close:before { content: "\e91f"; }
	.js .f8fl h1, .js .f8fl h2, .js .f8fl h3, .js .f8fl h4, .js .f8fl h5, .js .f8fl h6 { margin: 0 var(--margin_off); padding: 14px var(--rpp); border-top: 1px solid rgba(0,0,0,0); color: inherit; font-size: var(--main_fz); }
		.js[dir="ltr"] .f8fl h1, .js[dir="ltr"] .f8fl h2, .js[dir="ltr"] .f8fl h3, .js[dir="ltr"] .f8fl h4, .js[dir="ltr"] .f8fl h5, .js[dir="ltr"] .f8fl h6 { padding-right: 48px; }
		.js[dir="rtl"] .f8fl h1, .js[dir="rtl"] .f8fl h2, .js[dir="rtl"] .f8fl h3, .js[dir="rtl"] .f8fl h4, .js[dir="rtl"] .f8fl h5, .js[dir="rtl"] .f8fl h6 { padding-left: 48px; }
		.js .f8fl h1:before, .js .f8fl h2:before, .js .f8fl h3:before, .js .f8fl h4:before, .js .f8fl h5:before, .js .f8fl h6:before, .js .f8fl .check.switch:before, .js .f8fl .submit:before, .js .f8fl .link-btn:before { content: ""; display: block; position: absolute; left: 0; right: 0; top: -1px; border-top: 1px solid var(--custom_bd); }
		.js .f8fl h1.toggle, .js .f8fl h2.toggle, .js .f8fl h3.toggle, .js .f8fl h4.toggle, .js .f8fl h5.toggle, .js .f8fl h6.toggle { margin-bottom: -7px; }
		.js .f8fl .header-toggle { display: block; overflow: hidden; position: absolute; right: 0; top: 0; left: 0; z-index: 9; height: 100%; max-height: 44px; text-indent: -3000em; text-align: left; direction: ltr; }
			.js .f8fl .header-toggle:before { content: "\e904"; right: var(--rpp); left: auto; font-size: 6px; }
			.js .f8fl .toggle > .header-toggle:before { transform: rotate(180deg); }
		.js .f8fl h1 .strong, .js .f8fl h2 .strong, .js .f8fl h3 .strong, .js .f8fl h4 .strong, .js .f8fl h5 .strong, .js .f8fl h6 .strong { display: inline; margin: 0; padding: 0; font-size: 1em; }
			.js .f8fl h1.toggle .strong ~ .mobile-hide, .js .f8fl h2.toggle .strong ~ .mobile-hide, .js .f8fl h3.toggle .strong ~ .mobile-hide, .js .f8fl h4.toggle .strong ~ .mobile-hide, .js .f8fl h5.toggle .strong ~ .mobile-hide, .js .f8fl h6.toggle .strong ~ .mobile-hide { display: block; position: relative; left: 0; top: 0; padding: 17px 0 0; font-weight: var(--main_fw); font-size: 0.9285714286em; }
		.js .f8fl h1 a, .js .f8fl h2 a, .js .f8fl h3 a, .js .f8fl h4 a, .js .f8fl h5 a, .js .f8fl h6 a { font-size: var(--main_fz); }
		.js .f8fl header + h1, .js .f8fl header + h2, .js .f8fl header + h3, .js .f8fl header + h4, .js .f8fl header + h5, .js .f8fl header + h6 { border-top-width: 0; }
		.js .f8fl .check.switch { margin: 0 var(--rpn); padding: 9px var(--rpp) 5px; border-top: 1px solid rgba(0,0,0,0); }
		.js .f8fl .submit, .js .f8fl .link-btn { margin: 0 var(--rpn); padding: var(--rpp) var(--rpp) 12px; border-top: 1px solid rgba(0,0,0,0); }		
		/* Active input still visible */
		.js .f8fl .toggle + .check { margin-bottom: var(--rpp); }
		.js .f8fl .check.switch li, .js .f8fl .toggle + .check li { margin-bottom: 4px; }
			.js .f8fl .check { margin-top: -8px; margin-bottom: 8px; }
			.js #root .f8fl .m0-mobile:not(.toggle) + .check { margin-top: 0; margin-bottom: 0; }
			.js .f8fl .check label { padding: 0; font-size: var(--main_fz_small); }
			.js .f8fl .check.switch { margin-top: 0; margin-bottom: 0; }
				.js .f8fl .check.switch label { padding-right: 55px; font-size: 1em; }					
			.js .f8fl .toggle + .check { margin-top: 0; margin-bottom: var(--rpp); }
				.js .f8fl .toggle + .check label { padding-left: 26px; border: 0 solid rgba(0,0,0,0); font-size: 1em; }					
				.js .f8fl .toggle + .check label span { display: inline; }
 			 .js .f8fl .check label.strong { font-weight: var(--main_fw); }
		.js .f8fl .check li, .js .f8fl .toggle + .check.switch { margin-bottom: 0; }
		.js .f8fl .check label, .js .f8fl .check .link-more, .js .f8fl .toggle + .check .link-more.link-more-clicked, .js .f8fl .input-range, .js .f8fl .check label:before, .js .f8fl .check label span { display: none; }
		.js #root .f8fl h1:not(.toggle) + ul .link-more a, .js #root .f8fl h2:not(.toggle) + ul .link-more a, .js #root .f8fl h3:not(.toggle) + ul .link-more a, .js #root .f8fl h4:not(.toggle) + ul .link-more a, .js #root .f8fl h5:not(.toggle) + ul .link-more a, .js #root .f8fl h6:not(.toggle) + ul .link-more a { display: none; }
		.js .f8fl .toggle + .check label, .js .f8fl .toggle + .input-range, .js .f8fl .toggle + .input-range ~ .input-range, .js .f8fl .check.switch label, .js .f8fl .check.switch label:before, .js .f8fl .toggle + .check, .js .f8fl .toggle + .check label:before { display: block; }
		.js .f8fl .toggle + .check label.align-middle, .js .f8fl .toggle + .check input:checked ~ label.align-middle { display: flex; }
		.js .f8fl .toggle + .check li.link-more:nth-child(n+9), .js .f8fl .toggle + .check li.link-more:nth-child(n+9) a, .js .f8fl .toggle + .check .link-more { display: block; }
	.js .f8fl header { margin-left: var(--rpn); margin-right: var(--rpn); margin-bottom: -11px; padding-left: var(--rpp); padding-right: var(--rpp); }
		.js .f8fl header > h1, .js .f8fl header > h2, .js .f8fl header > h3, .js .f8fl header > h4, .js .f8fl header > h5, .js .f8fl header > h6 { display: block; overflow: hidden; height: 48px; margin: calc(0px - var(--pt)) var(--rpn) 11px; padding: 0 48px 0 var(--rpp); border-width: 0; background: var(--custom_top_main_bg); color: var(--custom_top_main_fg); font-size: calc(var(--main_fz) * 1.1428571429); /*line-height: 48px;*/ }
			.js .f8fl header > h1 a, .js .f8fl header > h2 a, .js .f8fl header > h3 a, .js .f8fl header > h4 a, .js .f8fl header > h5 a, .js .f8fl header > h6 a, .js .f8fl header > h1 a.strong, .js .f8fl header > h2 a.strong, .js .f8fl header > h3 a.strong, .js .f8fl header > h4 a.strong, .js .f8fl header > h5 a.strong, .js .f8fl header > h6 a.strong { display: block; float: right; font-size: var(--main_fz); line-height: var(--main_lh); }
		.js .f8fl header > ul { margin-bottom: 11px; }
			.js .f8fl header > ul:last-child > li:last-child { position: relative; z-index: 2; margin-left: var(--rpn); margin-right: var(--rpn); padding-left: var(--rpp); padding-right: var(--rpp); padding-bottom: 11px; }
			.js .f8fl header > ul:last-child > li:last-child { border-bottom: 1px solid rgba(0,0,0,0); }
	.js .f8fl header ul a:before, .js .f8fl header ul label:before, .js .f8fl header ul li.strong a { color: var(--custom_drop_nav_fg_hover); }
		
.f8fl-open #root > .overlay-close, .f8fl-open .shopify-section-header > .overlay-close, .f8fl-open .shopify-section-announcement-bar > .overlay-close { visibility: visible; opacity: 1; }

.js .m6cl > aside { --custom_input_bg: var(--custom_drop_nav_input_bg); --custom_input_bd: var(--custom_drop_nav_input_bd); --custom_input_fg: var(--custom_drop_nav_input_fg); --custom_input_pl: var(--custom_drop_nav_input_pl); }
 .f8fl-open #root .m6cl > aside { visibility: visible; opacity: 1; }
 .f8fl-open #root .m6cl > aside { transform: none; }

.f8fl { margin-bottom: 0; padding-bottom: 0; border-bottom-width: 0; }
	.f8fl .submit, .f8fl .link-btn { margin-right: 0; }
		.f8fl .submit:last-child , .f8fl .link-btn:last-child { margin-bottom: 0; }
	.f8fl button, .f8fl .link-btn a { width: 100%; margin-right: 0; }

.f8fl.processing .submit button, .f8fl.processing .submit a, .f8fl.processing .submit button *, .f8fl.processing .submit a * { color: rgba(0,0,0,0); }
.f8fl.processing .submit button:after, .f8fl.processing .submit a:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 18px; height: 18px; margin: -9px 0 0 -9px; border-radius: 20px; border: 2px solid var(--white); border-left-color: rgba(0,0,0,0) !important; }
	#root .f8fl.processing .submit .overlay-content[type="button"]:after, #root form.processing .submi.link-btn a.overlay-content:after { border-color: var(--primary_text); }
	.f8fl.processing .submit button:after, .f8fl.processing .submit a:after { animation-name: spin; animation-duration: .75s; animation-fill-mode: forwards; animation-iteration-count: infinite; animation-timing-function: linear; }
	
.js .f8fl header > h1, .js .f8fl header > h2, .js .f8fl header > h3, .js .f8fl header > h4, .js .f8fl header > h5, .js .f8fl header > h6, .js .f8fl .toggle + .input-range, .js .f8fl .toggle + .input-range ~ .input-range { display: flex; flex-wrap: wrap; align-items: center !important; }

.m6cl > aside .n6as { display: none; }
}

