/* Comparison Section Styles - Full Width */
.comparison-section {
  width: 100%;
  padding: 0;
  background-color: rgb(247, 250, 253);
  overflow: hidden;
  margin-bottom: 50px;
}

.comparison-container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

.comparison-content {
  display: flex;
  align-items: center;
  min-height: 500px;
  width: 100%;
}

.comparison-text {
  flex: 0 0 50%;
  width: 50%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.comparison-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather_, serif;
  font-size: 36px;
  font-weight: 700;
  letter-spacing: 0.12px;
  line-height: 50.4px;
  margin: 0 0 20px 0;
  outline: none;
  text-size-adjust: 100%;
  unicode-bidi: isolate;
  width: 100%;
  max-width: 440px;
}

.comparison-description {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Lato_, sans-serif;
  font-size: 16px;
  letter-spacing: 0.0278261px;
  line-height: 22.4px;
  margin: 0;
  outline: none;
  text-size-adjust: 100%;
  unicode-bidi: isolate;
  width: 100%;
  max-width: 385px;
}

.comparison-btn {
  appearance: button;
  background-color: rgb(0, 158, 224);
  background-position: 50% 50%;
  background-repeat: no-repeat;
  border: 1px solid rgb(0, 158, 224);
  border-radius: 4px;
  box-shadow: rgba(0, 158, 224, 0.4) 0px 4px 16px 0px;
  box-sizing: border-box;
  color: rgb(255, 255, 255);
  cursor: pointer;
  display: inline-block;
  font-family: Lato_, sans-serif;
  font-size: 16px;
  font-weight: 600;
  height: 50px;
  letter-spacing: 0.571429px;
  line-height: 24px;
  margin: 0;
  min-width: 200px;
  outline: none;
  overflow: visible;
  padding: 12px 30px;
  position: relative;
  text-align: center;
  text-decoration: none;
  text-size-adjust: 100%;
  text-transform: none;
  transition: all 0.2s linear;
  vertical-align: middle;
  width: 200px;
  word-spacing: 0px;
}

.comparison-btn:hover {
  background-color: rgb(0, 140, 200);
  border-color: rgb(0, 140, 200);
  box-shadow: rgba(0, 158, 224, 0.6) 0px 6px 20px 0px;
}

.comparison-guarantee {
  box-sizing: border-box;
  color: rgb(104, 104, 104);
  display: block;
  font-family: Lato_, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  margin: 12px 0 0 0;
  outline: none;
  text-align: center;
  text-size-adjust: 100%;
  unicode-bidi: isolate;
  width: 100%;
  max-width: 230px;
}

.comparison-image {
  flex: 0 0 50%;
  width: 50%;
  height: 600px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comparison-image img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100% !important;
  object-fit: cover;
  object-position: center center;
  border: none;
  outline: none;
  transition: opacity 0.3s ease;
  will-change: transform;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.comparison-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(245, 245, 245);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgb(153, 153, 153);
  font-family: Lato_, sans-serif;
  font-size: 16px;
}

.comparison-image-placeholder svg {
  margin-bottom: 10px;
}

/* Large Desktop */
@media (min-width: 1440px) {

  .comparison-title {
    font-size: 36px;
    line-height: 50px;
  }

  .comparison-description {
    font-size: 18px;
    line-height: 26px;
  }

  .comparison-content {
    min-height: 530px;
  }

  .comparison-image {
    height: 530px;
  }
}

/* Desktop */
@media (max-width: 1439px) and (min-width: 1025px) {
  .comparison-text {
    padding: 50px 40px 50px 80px;
  }
}

/* Tablet Landscape */
@media (max-width: 1024px) and (min-width: 769px) {
  .comparison-content {
    min-height: 450px;
  }

  .comparison-text {
    flex: 0 0 55%;
    width: 55%;
    padding: 40px 30px 40px 50px;
  }

  .comparison-image {
    flex: 0 0 45%;
    width: 45%;
    height: 650px;
  }

  .comparison-title {
    font-size: 32px;
    line-height: 44px;
  }

  .comparison-description {
    font-size: 15px;
    line-height: 21px;
  }
}

/* Tablet Portrait */
@media (max-width: 768px) and (min-width: 481px) {
  .comparison-content {
    flex-direction: column;
    min-height: auto;
  }

  .comparison-text {
    flex: none;
    width: 100%;
    padding: 40px 30px;
    text-align: center;
    order: 1;
  }

  .comparison-title {
    font-size: 28px;
    line-height: 38px;
    max-width: none;
    margin-bottom: 15px;
  }

  .comparison-description {
    max-width: none;
    margin-bottom: 10px;
  }

  .comparison-btn {
    min-width: 200px;
    width: 200px;
  }

  .comparison-guarantee {
    max-width: none;
  }

  .comparison-image {
    flex: none;
    width: 100%;
    height: 450px;
    order: 2;
  }
}

/* Mobile */
@media (max-width: 480px) {
  .comparison-content {
    flex-direction: column;
    min-height: auto;
  }

  .comparison-text {
    flex: none;
    width: 100%;
    padding: 0px 20px;
    text-align: center;
    order: 1;
  }

  .comparison-title {
    font-size: 24px;
    line-height: 32px;
    max-width: none;
    margin-bottom: 15px;
  }

  .comparison-description {
    font-size: 15px;
    line-height: 21px;
    max-width: none;
    margin-bottom: 10px;
  }

  .comparison-btn {
    min-width: 180px;
    width: 180px;
    font-size: 14px;
    padding: 10px 20px;
    height: 44px;
  }

  .comparison-guarantee {
    font-size: 14px;
    line-height: 20px;
    max-width: none;
  }

  .comparison-image {
    flex: none;
    width: 100%;
    height: 350px;
    order: 2;
  }
}

/* Performance optimizations */
.comparison-section {
  contain: layout style paint;
  will-change: auto;
}

.comparison-image {
  contain: layout style paint;
}

.comparison-image img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  transform-origin: center center;
}

/* WebP support optimization */
@supports (image-rendering: -webkit-optimize-contrast) {
  .comparison-image img {
    image-rendering: -webkit-optimize-contrast;
  }
}

@supports (image-rendering: crisp-edges) {
  .comparison-image img {
    image-rendering: crisp-edges;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .comparison-btn,
  .comparison-image img {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .comparison-title {
    color: #000000;
  }

  .comparison-description {
    color: #000000;
  }

  .comparison-btn {
    border-width: 2px;
  }
}
