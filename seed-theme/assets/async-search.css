:root {
	--custom_top_search_hw: var(--custom_top_search_h);
}
/* search_drop_desktop */
			#search > div, #search fieldset > div { position: absolute; left: var(--l0ra); right: var(--lar0); top: calc(100% + 16px); z-index: 3 !important; min-width: 100%; padding: 16px 15px 4px; box-shadow: 0 2px 2px rgba(0,0,0,.06); border-radius: var(--b2r); background: var(--custom_drop_nav_head_bg); color: var(--custom_drop_nav_fg); --custom_bd: var(--custom_drop_nav_bd); --custom_input_bd: var(--custom_drop_nav_bd); --price_color: inherit; --price_color_old: inherit; }
				#search > div.processing, #search fieldset > div.processing { height: 58px; padding: 0; }
				#search > div.processing *, #search fieldset > div.processing * { display: none; }
				#search .r6rt .rating > * .fill { background: var(--custom_drop_nav_head_bg); background: none; }
				#search div a { color: var(--custom_drop_nav_fg_hover); }
				#search div > p:not([class*="margin-"]) { margin-bottom: 8px; }
					#search .cols > p:first-child { opacity: .53; }
					#search div > p.strong:not([class*="margin-"]) { margin-bottom: 3px; }
					/*#search div > p.strong { margin-bottom: 3px; }
					#search div > p.strong.m5 { margin-bottom: 5px; }*/
					#search div > .cols > p ~ p a { white-space: nowrap; }
				#search div .l4pl { margin-bottom: 8px; font-weight: var(--main_fw); }
					#search div .l4pl span, #search div .l4pl .highlight { font-weight: var(--main_fw_strong); }
				#search div .l4ca { margin: /*-12px*/15px 0 15px; --offset: 72px; --img_d: 16px; }
					#search div .l4ca:first-child { margin-top: 0; }
					#search div .cols + .l4ca { margin-top: 8px; }
					#search div .l4ca li:after { display: block !important; }
					#search div .l4ca li:before { border-top: 1px solid var(--custom_drop_nav_bd); }
					/*[data-whatintent="mouse"] #root #search .l4ca.compact li:hover:before, [data-whatintent="mouse"] #root #search .l4ca.compact li:hover:after { left: -15px; right: -15px; }*/
					#search div p + .l4ca { margin-top: 0; }
					#root #search .l4ca .s1pr { color: inherit; }
					#root #search .l4ca .r6rt { margin-bottom: var(--mr_i); color: inherit; }
					#root #search .l4ca .r6rt .rating-label { opacity: .53; }
					#root #search .l4ca li.no-img { padding-left: 0; padding-right: 0; }
				#search .l4ca h1, #search .l4ca h2, #search .l4ca h3, #search .l4ca h4, #search .l4ca h5, #search .l4ca h6, #search .cols, #search .cols > * { position: static; }
			#search > a.toggle { display: none; overflow: hidden; position: absolute; left: -3000em; right: -3000em; top: 57px; z-index: 2; height: 100vh; background: var(--primary_text); opacity: .2; text-indent: -3000em; text-align: left; direction: ltr; }
			#search p > a.search-back { display: none; overflow: hidden; z-index: 2; position: absolute; left: 0; top: -2px; bottom: -2px; width: 38px; color: inherit; font-size: var(--size_8_f); text-decoration: none; text-indent: -3000em; text-align: left; direction: ltr; }
				#search p > a.search-back:before { content: "\e907"; }
				[data-theme="xpert"][dir="ltr"] #search p > a.search-back:before { content: "\e950"; font-size: var(--size_14_f); }
				[data-theme="xpert"][dir="rtl"] #search p > a.search-back:before { content: "\e96c"; font-size: var(--size_14_f); }
				@media only screen and (min-width: 760px) {
				.search-compact-active:not(.t1sr-mobile) #search p input { padding-left: 39px; }
				}
				#search :focus::placeholder { opacity: 1; }
			#search .clear-toggle { display: none; position: absolute; right: 44px; top: 0; z-index: 22; width: 34px; height: var(--custom_top_search_h); color: var(--gray_text); font-size: 18px; text-decoration: none; text-align: left; text-indent: -3000em; direction: ltr; }
				#search .clear-toggle:before { content: "\e961"; opacity: .4; }
				#search .clear-toggle:after { content: ""; display: block; position: absolute; left: -10px; right: 0; top: 0; bottom: 0; }
			#search.has-text > div, #search.has-text fieldset > div { display: block; }
				#search.has-text > div > .cols:last-child, #search.has-text fieldset > div > .cols:last-child { margin-bottom: 4px; }
				#search.has-text > div > p:last-child, #search.has-text fieldset > div > p:last-child { margin-bottom: 12px; }
				#search.has-text > div:empty, #search.has-text fieldset > div:empty { display: none; padding-top: 0; padding-bottom: 0; }
				#search.has-text > div.empty, #search.has-text fieldset > div.empty, #root #search.empty > div, #root #search.empty fieldset > div { display: none; padding-top: 0; padding-bottom: 0; }
			#search figure.img-multiply:before, #search picture.img-multiply:before { background: none; }
			#root #search.no-autocomplete > div, #search.no-autocomplete fieldset > div { display: none; }
			
/* before helpers */				
@media only screen and (min-width: 761px) {
.js #search.compact { visibility: visible; opacity: 1; pointer-events: auto; }
				
.search-compact-is-centered.search-compact-active:not(.no-search-overlay) #root > .overlay-close, .search-compact-is-centered.search-compact-active #top > .overlay-close, .search-compact-is-centered.search-compact-active:not(.no-search-overlay) .shopify-section-header #nav-bar > .overlay-close, .search-compact-is-centered.search-compact-active:not(.no-search-overlay) .shopify-section-header #nav > .overlay-close { visibility: visible; opacity: 1; }
.search-compact-is-centered.search-compact-active:not(.no-search-overlay) #header > .overlay-close, .search-compact-is-centered.search-compact-active:not(.no-search-overlay) .shopify-section-announcement-bar > .overlay-close, .search-compact-is-centered.search-compact-active:not(.no-search-overlay) #nav-top > .overlay-close { visibility: visible; opacity: 1; }

.has-first-m6fr-wide.search-compact-is-centered #root .shopify-section-header.transparent > .overlay-close { visibility: hidden; opacity: 0; }

.search-compact-is-centered.has-first-m6fr-wide:not(.no-search-overlay) #header > .overlay-close, 
.search-compact-is-centered.has-first-m6fr-wide:not(.no-search-overlay) #nav > .overlay-close, 
.search-compact-is-centered.has-first-m6fr-wide:not(.no-search-overlay) #nav-bar > .overlay-close, #header-inner #nav-outer > * > .overlay-close { background: none; }
[data-whatintent="mouse"].tr_hh.search-compact-is-centered.has-first-m6fr-wide:not(.no-search-overlay) .shopify-section-header.tr_h #header > .overlay-close, 
[data-whatintent="mouse"].tr_hh.search-compact-is-centered.has-first-m6fr-wide:not(.no-search-overlay) .shopify-section-header.tr_h #nav > .overlay-close, 
[data-whatintent="mouse"].tr_hh.search-compact-is-centered.has-first-m6fr-wide:not(.no-search-overlay) .shopify-section-header.tr_h #nav-bar > .overlay-close { background: rgba(var(--primary_text_rgb), .2); }

.search-compact-is-centered.search-compact-active.has-first-m6fr-wide:not(.no-search-overlay) #header-inner > *:not(#search), .search-compact-is-centered.search-compact-active.has-first-m6fr-wide:not(.no-search-overlay) #header-outer > #nav-bar { opacity: .8; }
}


@media only screen and (min-width: 761px) {
	#root .shopify-section-header #header-inner > #search.text-center-sticky.compact { 
		position: absolute; left: 50%; z-index: 98; width: 100%; margin-left: 0; margin-right: 0;
		transform: translateX(-50%);
	}
	.search-compact-active #root .shopify-section-header #header-inner > #search.text-center-sticky.compact { 
		top: 50%;
		transform: translate(-50%, -50%);
	}
	.search-compact-active #root .shopify-section-header #header-inner > #search.text-center-sticky.compact.wide { position: fixed; left: 0; right: 0; top: 0; bottom: auto; z-index: 100; width: 100%; max-width: none; max-width: none; max-height: 100%; margin: 0; padding: 0; border-width: 0; transform: none; --custom_top_search_h: var(--custom_top_search_hw); }
	.search-compact-active #root .shopify-section-header #header-inner > #search.text-center-sticky.compact.wide input { border-width: 0; border-radius: 0; }
	.search-compact-active #root .shopify-section-header #header-inner > #search:not(.compact-mobile).text-center-sticky.compact { z-index: 99; }
}
@media only screen and (min-width: 761px) {
.search-compact-active.has-search-wide #header-outer { z-index: 100; }

.search-compact-is-centered.search-compact-active .shopify-section-header > .overlay-close { visibility: hidden; opacity: 0; }
.search-compact-is-centered.search-compact-active .shopify-section-header #nav > .overlay-close { visibility: hidden; opacity: 0; }
.no-search-overlay.search-compact-is-centered.search-compact-active:not(.search-full, .m6pn-open) #root > .overlay-close, .no-search-overlay.search-compact-is-centered.search-compact-active:not(.search-full, .m6pn-open) .shopify-section-header #nav > .overlay-close, .no-search-overlay.search-compact-is-centered.search-compact-active:not(.search-full, .m6pn-open) .shopify-section-header #nav-bar > .overlay-close, .no-search-overlay.search-compact-is-centered.search-compact-active:not(.search-full, .m6pn-open) .shopify-section-header > .overlay-close { visibility: hidden; opacity: 0; }
.has-first-m6fr-wide.search-full:not(.no-search-overlay) #header-outer > [id*="nav"] > .overlay-close { display: none; }
[data-theme="xpert"].has-first-m6fr-wide.search-full:not(.no-search-overlay, .tr_hh) #header-outer > [id*="nav"] { -webkit-filter: blur(3px); filter: blur(3px); pointer-events: none; }
[data-whatintent="mouse"].tr_hh.has-first-m6fr-wide.search-full:not(.no-search-overlay) #header-outer > [id*="nav"] > .overlay-close { display: block; }

.search-compact-active.search-blur .overlay-close { -webkit-backdrop-filter: blur(3px); backdrop-filter: blur(3px); }

#search.wide.text-center-sticky > div, #search.wide.text-center-sticky fieldset > div { overflow-x: hidden; overflow-y: auto; top: 100%; max-height: calc(100vh - var(--custom_top_search_h)); padding-left: var(--rpp); padding-right: var(--rpp); border-top: 1px solid var(--custom_input_bd); border-radius: 0; }
#search.wide.text-center-sticky .l4ca { 
	margin-left: var(--rpn); border-bottom-width: 0;
	display: flex; flex-wrap: wrap; 
	flex-direction: row;
}
	#search.wide.text-center-sticky .l4ca > li { width: 50%; border-left: var(--rpp) solid rgba(0,0,0,0); } 
	#search.wide.text-center-sticky .l4ca > li:before { border-top-width: 0; border-bottom-width: 1px; }
	#search.wide.text-center-sticky .l4ca > li:first-child:before, #search.wide.text-center-sticky .l4ca > li:first-child + li:before { border-top-width: 1px; }
	/*[data-whatintent="mouse"] #search.wide.text-center-sticky .l4ca.compact > li:hover + li:before { border-top-width: 1px; }*/
	
#search.solid > div, #search.solid fieldset > div { top: 100%; border-top-left-radius: 0; border-top-right-radius: 0; }
#search.has-text.solid p input { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
}

/* helpers */
#root #search div .clear-toggle, #root #search div .search-back, .search-compact-active .search-compact-toggle, .search-compact-active .search-compact-cont { display: none; }
#search.not-empty .clear-toggle, .search-compact-active #search p > a.search-back { display: block; z-index: 98; }
#search div .l4ca:first-child { margin-top: 0; }
			

@media only screen and (max-width: 760px) { /* 760 */
/*.has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide:first-child.im-tr { margin-top: calc(0px - var(--content_p) - var(--header_height_static) - var(--custom_top_search_h) - 24px); }
.has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide:first-child.im-tr article { padding-top: calc(var(--pdc) + var(--header_height_static) + var(--custom_top_search_h) + 24px); }*/

.search-compact-active:not(.t1sr-desktop) #root .shopify-section-header #search > p, .search-compact-active:not(.t1sr-desktop) #root .shopify-section-header #search > fieldset > p { padding-left: calc(45px - var(--rpp)); }
.search-compact-active #root .shopify-section-header #search.full:not(.no-autocomplete) > p, .search-compact-active #root .shopify-section-header #search.full:not(.no-autocomplete) > fieldset > p { padding-left: 0; padding-right: 0; }

.search-compact-active.t1sr #search, .search-compact-active .t1sr #search, .search-compact-active.t1sr .search-compact-cont ~ #search, .search-compact-active .t1sr .search-compact-cont ~ #search { display: block; }

.search-compact-active:not(.has-mobile-visible-search, .has-search-wide, .m-pos-up) .shopify-section-header { margin-bottom: calc(var(--custom_top_search_h) + 24px); } 
.search-compact-active:not(.has-mobile-visible-search, .has-search-wide, .m-pos-up) .shopify-section-header.no-bd-m { margin-bottom: calc(var(--custom_top_search_h) + 12px); } 
.search-compact-active.m-pos-up:not(.search-full-mode) #root > .overlay-close, .search-compact-active.m-pos-up:not(.search-full-mode) .shopify-section-header > .overlay-close, .search-compact-active.m-pos-up:not(.search-full-mode) .shopify-section-announcement-bar .overlay-close { visibility: visible; opacity: 1; }
.search-compact-active.m-pos-up.search-full-mode #root > .overlay-close, .search-compact-active.m-pos-up.search-full-mode .shopify-section-header > .overlay-close, .search-compact-active.m-pos-up.search-full-mode .shopify-section-announcement-bar .overlay-close { display: none; }
	
	#root .shopify-section-header #search.full:not(.no-autocomplete) { position: fixed; left: 0; right: 0; top: 0; bottom: 0; z-index: 99; width: auto; padding: 0 0 .1px; background: var(--custom_drop_nav_head_bg); }
		#root .shopify-section-header #search.full:not(.no-autocomplete) > p, #root .shopify-section-header #search.full:not(.no-autocomplete) > fieldset > p { border: 20px solid var(--sand); border-top-width: 12px; border-bottom-width: 12px; border-left-width: 45px; background: var(--sand); }
		#search.full > div, #search.full fieldset > div { overflow-x: hidden; overflow-y: auto; left: 0; right: 0; min-width: 0; width: 100%; height: calc(100vh - var(--custom_top_search_h) - 24px); box-shadow: none; border-radius: 0; }
		#search.full > div.processing, #search.full fieldset > div.processing { height: calc(100vh - var(--custom_top_search_h) - 24px); }
		#search div .l4ca + * { margin-top: 0; padding-top: 0; border-top-width: 0; }
			#root #search div .l4ca .s1pr { top: 0; }
	.search-compact-active #root .shopify-section-header #search.m-pos-up:not(.no-autocomplete, .full) { position: fixed; left: 0; right: 0; top: 0; z-index: 324; width: auto; padding-left: var(--rpp); padding-right: var(--rpp); }
	#search .clear-toggle { right: 40px; height: var(--custom_top_search_h); }
	#search p > a.search-back { left: var(--rpn); width: 45px; color: var(--custom_top_search_fg); }	
	#search > a.toggle { position: absolute; left: 0; right: auto; bottom: auto; top: 0; z-index: 99; width: 45px; height: calc(var(--custom_top_search_h) + 24px); background: none; color: var(--custom_top_search_fg); font-size: var(--size_8_f); opacity: 1; }
		#search > a.toggle:before { content: "\e907"; }
			[data-theme="xpert"][dir="ltr"] #search > a.toggle:before { content: "\e950"; font-size: var(--size_14_f); }
			[data-theme="xpert"][dir="rtl"] #search > a.toggle:before { content: "\e96c"; font-size: var(--size_14_f); }
		#search > div, #search fieldset > div { position: relative; top: 0; }
		.search-full #search > a.toggle, #search.full > a.toggle { display: block; }
		.search-full #search > a.toggle ~ a.toggle, #search.full > a.toggle ~ a.toggle, .search-full #search.no-autocomplete > a.toggle, #search.full.no-autocomplete > a.toggle { display: none; }
	.t1sr.search-compact-active #content, .search-compact-active .t1sr ~ #content { margin-top: 0; }

	.search-compact-active #root #search { top: 100%; }

.search-full-mode:not(:has(#search.no-autocomplete)) { overflow: hidden; }

.shopify-section-header:has(#header-inner.mobile-visible-search) > .overlay-close { bottom: calc(0px - var(--custom_top_search_h) - var(--mob_cl)); } 
.shopify-section-header.has-mobile-visible-search > .overlay-close { bottom: calc(0px - var(--custom_top_search_h) - var(--mob_cl)); }

#search.full:not(.no-autocomplete) p > a.search-back, .search-compact-active.t1sr-desktop #search p > a.search-back { display: none; }
#root .shopify-section-header #search.full:not(.no-autocomplete):before, .search-full #nav-bar > .overlay-close, .l4us .swiper-button-prev, .l4us .swiper-button-next, #root .shopify-section-header #search.full:not(.no-autocomplete):before { display: none; }
}

/*.shopify-section-header:has(#search.no-pd-t) { --pdc: 1; }
.shopify-section-header.no-pd-t { --pdc: 1; }*/

/*.shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-bg:not(.bd-b)) { --mob_cl: var(--search_mob_pd); } 
.shopify-section-header.has-mobile-visible-search.no-bd-m { --mob_cl: var(--search_mob_pd); } 		

.shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-pd-t) { --mob_cl: var(--search_mob_pd); } 
.shopify-section-header.has-mobile-visible-search.no-pd-t { --mob_cl: var(--search_mob_pd); } */
			
			
form.processing .submit { }
	#search.processing button:before { color: rgba(0,0,0,0); }
	#search.processing button:after, #search > div.processing:after, #search fieldset > div.processing:after { 
		content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 18px; height: 18px; margin: -9px 0 0 -9px; border-radius: 20px; border: 2px solid var(--white); border-left-color: rgba(0,0,0,0) !important;
		animation-name: spin; animation-duration: .75s; animation-fill-mode: forwards; animation-iteration-count: infinite; animation-timing-function: linear; }
		#search.processing button:after { border-color: var(--custom_top_search_fg); border-left-color: rgba(0,0,0,0); }
		#search.processing button:after { width: 16px; height: 16px; margin-top: -8px; }
		#search > div.processing:after, #search fieldset > div.processing:after { border-color: var(--primary_text); }


@keyframes spin { 0% { transform: none; } 100% { transform: rotate(360deg); } }