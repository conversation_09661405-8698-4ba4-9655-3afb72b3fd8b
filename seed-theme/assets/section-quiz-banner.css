/* Quiz Banner Section */
.quiz-banner-section {
  background: #00334b !important;
  position: relative;
  z-index: 1;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  overflow: hidden;
  min-height: 200px;
  margin-top: 50px
}

.quiz-banner-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #00334b !important;
  z-index: -1;
}

.quiz-banner-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.quiz-banner-title {
  box-sizing: border-box;
  color: rgb(255, 255, 255) !important;
  display: block;
  font-family: Merriweather_, serif;
  font-size: 36px;
  font-weight: 700;
  height: auto;
  letter-spacing: 0.12px;
  line-height: 54px;
  margin-bottom: 20px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 0px;
  max-width: 690px;
  text-align: center;
  width: 100%;
}

.quiz-banner-button-wrapper {
  display: flex !important;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  width: 100%;
  position: relative;
  z-index: 10;
}

.quiz-banner-button,
.quiz-banner-section .quiz-banner-button,
.quiz-banner-section a.quiz-banner-button {
  background-color: rgb(0, 158, 224) !important;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  border: 1px solid rgb(0, 158, 224) !important;
  border-radius: 4px;
  box-shadow: rgba(0, 158, 224, 0.4) 0px 4px 16px 0px !important;
  box-sizing: border-box;
  color: rgb(255, 255, 255) !important;
  cursor: pointer;
  display: inline-block !important;
  font-family: Lato_, sans-serif;
  font-size: 16px;
  font-weight: 600;
  height: 50px;
  letter-spacing: 0.571429px;
  line-height: 24px;
  min-width: 200px;
  padding: 12px 15px;
  position: relative;
  text-align: center;
  text-decoration: none !important;
  transition: all 0.2s linear;
  vertical-align: middle;
  width: auto;
  z-index: 10;
  opacity: 1 !important;
  visibility: visible !important;
}

.quiz-banner-button:hover,
.quiz-banner-section .quiz-banner-button:hover,
.quiz-banner-section a.quiz-banner-button:hover {
  background-color: rgb(0, 142, 202) !important;
  border-color: rgb(0, 142, 202) !important;
  text-decoration: none !important;
  color: rgb(255, 255, 255) !important;
  box-shadow: rgba(0, 158, 224, 0.6) 0px 6px 20px 0px !important;
}

.quiz-banner-button:active {
  box-shadow: rgba(0, 158, 224, 0.4) 0px 4px 16px 0px !important;
}

/* Force dark theme for quiz banner - override all color schemes */
.quiz-banner-section,
.quiz-banner-section[class*="palette-"] {
  background: #00334b !important;
}

.quiz-banner-section .quiz-banner-title,
.quiz-banner-section[class*="palette-"] .quiz-banner-title {
  color: rgb(255, 255, 255) !important;
}

.quiz-banner-section .quiz-banner-button,
.quiz-banner-section[class*="palette-"] .quiz-banner-button {
  background-color: rgb(0, 158, 224) !important;
  border-color: rgb(0, 158, 224) !important;
  color: rgb(255, 255, 255) !important;
}

.quiz-banner-section .quiz-banner-button:hover,
.quiz-banner-section[class*="palette-"] .quiz-banner-button:hover {
  background-color: rgb(0, 142, 202) !important;
  border-color: rgb(0, 142, 202) !important;
  color: rgb(255, 255, 255) !important;
}

/* Mobile Responsive */
@media only screen and (max-width: 768px) {
  .quiz-banner-title {
    font-size: 28px;
    line-height: 42px;
    margin-bottom: 15px;
    padding: 0 10px;
  }
  
  .quiz-banner-button {
    min-width: 180px;
    font-size: 14px;
    padding: 10px 12px;
    height: 44px;
  }
  
  .quiz-banner-content {
    padding: 0 15px;
  }
}

@media only screen and (max-width: 480px) {
  .quiz-banner-title {
    font-size: 24px;
    line-height: 36px;
    margin-bottom: 20px;
  }
  
  .quiz-banner-button {
    width: 100%;
    max-width: 280px;
    min-width: auto;
  }
  
  .quiz-banner-button-wrapper {
    margin-top: 25px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .quiz-banner-button {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .quiz-banner-button {
    border-width: 2px;
  }
  
  .quiz-banner-title {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
}

/* Focus states for accessibility */
.quiz-banner-button:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

.quiz-banner-button:focus:not(:focus-visible) {
  outline: none;
}

.quiz-banner-button:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}


