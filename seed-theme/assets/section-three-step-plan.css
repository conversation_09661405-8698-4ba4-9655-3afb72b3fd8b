/* Three Step Plan Section */
.three-step-plan-section {
  margin: 60px 0;
  padding: 0;
}

.three-step-plan-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather_, serif;
  font-size: 48px;
  font-weight: 400;
  letter-spacing: 0.12px;
  line-height: 67.2px;
  margin: 0 0 50px 0;
  text-align: center;
  text-size-adjust: 100%;
  unicode-bidi: isolate;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.three-step-plan-cards {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 50px;
  flex-wrap: wrap;
}

.step-card {
  flex: 0 1 320px;
  max-width: 320px;
  text-align: center;
}

.step-card-image {
  margin-bottom: 20px;
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(190, 235, 252);
}

.step-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 8px;
}

.step-card-placeholder {
  width: 100%;
  height: 200px;
  background-color: rgb(190, 235, 252);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-card-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Lato_, sans-serif;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0.0388889px;
  line-height: 25.2px;
  margin: 0 0 5px 0;
  text-align: center;
  text-size-adjust: 100%;
  unicode-bidi: isolate;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.step-card-description {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: inline;
  font-family: Lato_, sans-serif;
  font-size: 15.3333px;
  font-weight: 500;
  line-height: 21.4667px;
  text-align: center;
  text-size-adjust: 100%;
  margin: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.three-step-plan-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.three-step-plan-btn {
  appearance: button;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  font-family: Lato_, sans-serif;
  font-size: 16px;
  font-weight: 600;
  height: 48px;
  letter-spacing: 0.571429px;
  line-height: 24px;
  margin: 15px;
  min-width: 228px;
  padding: 11px 10px;
  position: relative;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s linear;
  vertical-align: middle;
  width: 228px;
  border-radius: 4px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.three-step-plan-btn-primary {
  background-color: rgb(0, 158, 224);
  border: 1px solid rgb(0, 158, 224);
  box-shadow: rgba(0, 158, 224, 0.4) 0px 4px 16px 0px;
  color: rgb(255, 255, 255);
}

.three-step-plan-btn-primary:hover {
  background-color: rgb(20, 168, 234);
  border-color: rgb(20, 168, 234);
}

.three-step-plan-btn-secondary {
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid rgb(0, 158, 224);
  box-shadow: none;
  color: rgb(0, 158, 224);
}

.three-step-plan-btn-secondary:hover:not(.disabled) {
  background-color: rgba(0, 158, 224, 0.05);
  border-color: rgb(20, 168, 234);
  color: rgb(20, 168, 234);
}

.three-step-plan-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Override theme effects for all buttons */
.three-step-plan-btn,
.three-step-plan-btn-primary,
.three-step-plan-btn-secondary,
.three-step-plan-video-btn {
  animation: none !important;
  transform: none !important;
  filter: none !important;
  backdrop-filter: none !important;
}

.three-step-plan-btn::before,
.three-step-plan-btn::after,
.three-step-plan-btn-primary::before,
.three-step-plan-btn-primary::after,
.three-step-plan-btn-secondary::before,
.three-step-plan-btn-secondary::after,
.three-step-plan-video-btn::before,
.three-step-plan-video-btn::after {
  display: none !important;
  content: none !important;
  animation: none !important;
  transition: none !important;
  box-shadow: none !important;
  background: none !important;
}

.three-step-plan-btn:hover,
.three-step-plan-btn-primary:hover,
.three-step-plan-btn-secondary:hover,
.three-step-plan-video-btn:hover {
  animation: none !important;
  transform: none !important;
}

/* Video Button Styles - Override all theme styles */
.three-step-plan-video-btn {
  /* Reset all inherited styles */
  all: unset !important;

  /* Set specific styles - EXACTLY same as .three-step-plan-btn */
  appearance: button !important;
  box-sizing: border-box !important;
  cursor: pointer !important;
  display: inline-block !important;
  font-family: Lato_, sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  height: 48px !important;
  letter-spacing: 0.571429px !important;
  line-height: 24px !important;
  margin: 15px !important;
  min-width: 228px !important;
  padding: 11px 10px !important;
  position: relative !important;
  text-align: center !important;
  text-decoration: none !important;
  transition: none !important;
  vertical-align: middle !important;
  width: 228px !important;
  border-radius: 4px !important;
  background-color: rgba(0, 0, 0, 0) !important;
  background: rgba(0, 0, 0, 0) !important;
  border: 1px solid rgb(0, 158, 224) !important;
  box-shadow: none !important;
  color: rgb(0, 158, 224) !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;

  /* Disable all animations and effects */
  animation: none !important;
  transform: none !important;
  filter: none !important;
  backdrop-filter: none !important;
}

.three-step-plan-video-btn:hover:not(.disabled) {
  background-color: rgba(0, 158, 224, 0.05) !important;
  background: rgba(0, 158, 224, 0.05) !important;
  border-color: rgb(20, 168, 234) !important;
  color: rgb(20, 168, 234) !important;
  box-shadow: none !important;
  animation: none !important;
  transform: none !important;
  transition: none !important;
}

.three-step-plan-video-btn.disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

/* Video Modal Styles - Optimized for performance */
.video-modal {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  align-items: center;
  justify-content: center;
  /* Performance optimizations */
  will-change: opacity;
  contain: layout style paint;
}

.video-close-btn {
  /* Reset all inherited styles */
  all: unset !important;

  /* Set specific styles */
  position: absolute !important;
  top: 20px !important;
  right: 20px !important;
  background: rgba(0, 0, 0, 0.5) !important;
  border: none !important;
  color: white !important;
  font-size: 30px !important;
  width: 40px !important;
  height: 40px !important;
  cursor: pointer !important;
  z-index: 10001 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.2s ease !important;
  font-weight: 300 !important;
  line-height: 1 !important;
  font-family: Arial, sans-serif !important;
  border-radius: 50% !important;
}

.video-close-btn:hover {
  background: rgba(0, 0, 0, 0.8) !important;

  /* Disable all animations and effects */
  animation: none !important;
  transform: none !important;
  filter: none !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
}

.video-close-btn:hover {
  opacity: 0.7 !important;
}

.video-close-btn::before,
.video-close-btn::after {
  display: none !important;
  content: none !important;
}

.video-container {
  position: relative;
  width: 90%;
  max-width: 1200px;
  aspect-ratio: 16 / 9;
  background: #000;
  /* Performance optimizations */
  contain: layout;
}

.video-container video,
.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  /* Performance optimizations */
  will-change: transform;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .three-step-plan-section {
    margin: 40px 0;
    padding: 0 15px;
  }

  .three-step-plan-title {
    font-size: 32px;
    line-height: 44px;
    margin-bottom: 40px;
  }

  .three-step-plan-cards {
    gap: 30px;
    margin-bottom: 40px;
  }

  .step-card {
    flex: 1 1 100%;
    max-width: 100%;
  }

  .three-step-plan-btn,
  .three-step-plan-video-btn {
    margin: 10px;
    min-width: 200px;
    width: 200px;
  }

  /* Video Modal Mobile */
  .video-container {
    width: 95%;
  }

  .video-close-btn {
    top: 15px;
    right: 15px;
    width: 35px;
    height: 35px;
    font-size: 25px;
  }
}

@media (max-width: 480px) {
  .three-step-plan-section {
    margin: 30px 0;
  }

  .three-step-plan-title {
    font-size: 28px;
    line-height: 38px;
    margin-bottom: 30px;
  }

  .three-step-plan-cards {
    gap: 25px;
    margin-bottom: 35px;
  }

  .three-step-plan-btn,
  .three-step-plan-video-btn {
    min-width: 180px;
    width: 180px;
    font-size: 15px;
  }

  .video-close-btn {
    top: 10px !important;
    right: 10px !important;
    width: 30px !important;
    height: 30px !important;
    font-size: 22px !important;
  }
}

/* High specificity overrides for theme protection */
#root .three-step-plan-section .three-step-plan-video-btn,
button.three-step-plan-video-btn {
  all: unset !important;
  /* Copy base styles */
  appearance: button !important;
  box-sizing: border-box !important;
  cursor: pointer !important;
  display: inline-block !important;
  font-family: Lato_, sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  height: 48px !important;
  letter-spacing: 0.571429px !important;
  line-height: 24px !important;
  margin: 15px !important;
  min-width: 228px !important;
  padding: 11px 10px !important;
  position: relative !important;
  text-align: center !important;
  vertical-align: middle !important;
  width: 228px !important;
  border-radius: 4px !important;
  background: rgba(0, 0, 0, 0) !important;
  border: 1px solid rgb(0, 158, 224) !important;
  color: rgb(0, 158, 224) !important;
}

/* High specificity styles only for desktop */
@media (min-width: 769px) {
  #root .three-step-plan-section .video-close-btn,
  button.video-close-btn {
    all: unset !important;
    position: absolute !important;
    top: 100px !important;
    right: 190px !important;
    color: white !important;
    font-size: 30px !important;
    width: 40px !important;
    height: 40px !important;
    cursor: pointer !important;
    z-index: 10001 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 300 !important;
    line-height: 1 !important;
    font-family: Arial, sans-serif !important;
    background: rgba(0, 0, 0, 0.5) !important;
    border-radius: 50% !important;
    transition: background-color 0.2s ease !important;
  }

  #root .three-step-plan-section .video-close-btn:hover,
  button.video-close-btn:hover {
    background: rgba(0, 0, 0, 0.8) !important;
  }
}

/* Mobile responsive overrides */
@media (max-width: 768px) {
  .three-step-plan-video-btn,
  #root .three-step-plan-section .three-step-plan-video-btn {
    margin: 10px !important;
    min-width: 200px !important;
    width: 200px !important;
  }

  .video-close-btn,
  #root .three-step-plan-section .video-close-btn,
  button.video-close-btn {
    /* Top right corner for tablet - high specificity */
    all: unset !important;
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    color: white !important;
    width: 35px !important;
    height: 35px !important;
    font-size: 25px !important;
    cursor: pointer !important;
    z-index: 10001 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 300 !important;
    line-height: 1 !important;
    font-family: Arial, sans-serif !important;
  }
}

@media (max-width: 480px) {
  .three-step-plan-video-btn,
  #root .three-step-plan-section .three-step-plan-video-btn {
    min-width: 180px !important;
    width: 180px !important;
    font-size: 15px !important;
    margin: 10px !important;
  }

  .video-close-btn,
  #root .three-step-plan-section .video-close-btn,
  button.video-close-btn {
    /* Top right corner for mobile phones - high specificity */
    all: unset !important;
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    color: white !important;
    width: 30px !important;
    height: 30px !important;
    font-size: 22px !important;
    cursor: pointer !important;
    z-index: 10001 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 300 !important;
    line-height: 1 !important;
    font-family: Arial, sans-serif !important;
  }
}
