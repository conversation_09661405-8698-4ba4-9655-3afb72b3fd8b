/**
 * Estonian translation for bootstrap-datepicker
 * <PERSON><PERSON> <https: //github.com/anroots>
 * Fixes by <PERSON><PERSON><PERSON> <<https: //github.com/ragulka>
 */
(function () {
  Datepicker.locales.et = {
    days: ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
    daysShort: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>el<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
    daysMin: ["P", "E", "T", "K", "N", "R", "L"],
    months: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "April<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September", "Oktoober", "November", "Detsember"],
    monthsShort: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sept", "<PERSON><PERSON>", "Nov", "Det<PERSON>"],
    today: "<PERSON>äna",
    clear: "Tühje<PERSON>",
    weekStart: 1,
    format: "dd.mm.yyyy"
  };
}());
