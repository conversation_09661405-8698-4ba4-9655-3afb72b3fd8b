#root .l4hs > li.toggle { z-index: 9; }
.l4hs > li { z-index: 8; }
#root .l4hs > li > a:after { --secondary_bg_btn: var(--bg); }
[data-whatintent="mouse"] #root .l4hs > li > a:hover:after { box-shadow: 0 0 15px 5px rgba(0,0,0,0); animation: pulse .75s; }


#root .m6fr figure.has-l4hs { overflow: visible; }
.l4hs { color: var(--primary_text); font-size: var(--main_fz); text-indent: 0; text-align: var(--text_align_start); --btn_dist: 10px; --pd: 12px; }
#root .l4hs > li > div {
	visibility: hidden; position: absolute; left: 50%; top: 50%; z-index: 1; width: 294px; padding: var(--pd) var(--pd) max(0.1px, calc(var(--pd) - var(--new_mr))); box-shadow: 0 2px 4px rgba(0,0,0,.1); border-radius: var(--b2r); border: 4px solid var(--primary_bg); background: var(--primary_bg); opacity: 0; scrollbar-width: thin; --new_mr: 2px;
	display: flex;
	flex-direction: column;
}
#root .l4hs > li > div p, #root .l4hs > li > div ul, #root .l4hs > li > div h1, #root .l4hs > li > div h2, #root .l4hs > li > div h3, #root .l4hs > li > div h4, #root .l4hs > li > div h5, #root .l4hs > li > div h6 { --main_mr: var(--new_mr); }
#root .l4hs > li > div::-webkit-scrollbar { width: 6px; height: 6px; }
#root .l4hs > li > div::-webkit-scrollbar-track { background: none; }
#root .l4hs > li > div::-webkit-scrollbar-thumb { background: var(--alto); }
#root .l4hs > li.inv > div { left: auto; right: 50%; }
#root .l4hs > li.inv-v > div { top: auto; bottom: 50%; }
[dir="rtl"] #root .l4hs > li > div { left: auto; right: 50%; }
[dir="rtl"] #root .l4hs > li.inv > div { left: 50%; right: auto; }
#root .l4hs > li.toggle > div { visibility: visible; opacity: 1; transform: none; }
.l4hs > li > div .r6rt .rating > * .fill { --body_bg: var(--primary_bg) }
.l4hs .info { padding: 3px 0 4px; color: var(--primary_text); font-size: var(--main_fz); line-height: var(--main_lh_l); }
#root .l4hs li > div:has(.info) { overflow-x: hidden; overflow-y: auto; width: 300px; max-height: 170px; }
#root .l4hs li.has-info > div { overflow-x: hidden; overflow-y: auto; width: 300px; max-height: 170px; }
#root .l4hs p + p > a.strong:first-child { margin-top: 0; }
.l4hs .info p { font-size: var(--main_fz); }
.l4hs figure { left: 0 !important; right: 0 !important; margin-bottom: var(--pd); }
.l4hs p + figure { margin-top: calc(var(--pd) - var(--new_mr)); }
.l4hs h1, .l4hs h2, .l4hs h3, .l4hs h4, .l4hs h5, .l4hs h6 {  margin-bottom: var(--main_mr_h);/*margin-bottom: var(--new_mr); font-weight: var(--main_fw_h); font-family: var(--main_ff); font-size: 1em; line-height: var(--main_lh_l);*/ }
.l4hs h1 .small, .l4hs h2 .small, .l4hs h3 .small, .l4hs h4 .small, .l4hs h5 .small, .l4hs h6 .small { display: block; margin: 0 0 calc(var(--main_mr) * 0.4615384615); font-weight: var(--main_fw); font-size: var(--main_fz); opacity: .53; }
.l4hs .link-btn, .l4hs .submit { position: relative; left: 0; right: 0; top: 0; bottom: 0; z-index: 99; margin-top: 8px; }
.l4hs .link-btn a { flex-grow: 3; }
.l4hs .r6rt { margin-bottom: 1px; font-weight: var(--main_fw); font-size: calc(var(--main_fz) * 0.8571428571); }
.l4hs .r6rt .rating-label { opacity: .53; }
.l4hs .r6rt .rating { top: .175em; margin-bottom: 3px; }
.l4hs p { font-size: var(--main_fz); font-weight: var(--main_fw); }
/*.l4hs p:not(.link-btn) { margin-bottom: 2px; }*/
.l4hs p[class*="overlay"] { font-weight: var(--main_fw); font-size: calc(var(--main_fz) * 0.8571428571); }
.l4hs .overlay-gray { } { color: inherit; opacity: .53; }
.l4hs .info p:not(.link-btn, .submit, .r6rt) a { color: var(--secondary_bg); }
.l4hs .price { color: var(--price_color); font-weight: var(--price_fw); font-size: var(--price_fz); font-family: var(--price_ff); font-style: var(--price_fs); line-height: var(--price_lh); letter-spacing: var(--price_ls); }
.l4hs .price .small { display: block; margin: 1px 0 2px; color: var(--primary_text_h); font-family: var(--main_ff); font-size: var(--main_fz_small); font-weight: var(--main_fw); font-size: 0.8571428571em; letter-spacing: var(--main_ls); text-decoration: none; opacity: .53; }
.l4hs .price span[class*="overlay"]:not(.old-price, .s1bx) { display: block; margin: 0 0 4px; font-size: var(--main_fz_small); text-decoration: none; opacity: 1; }
.l4hs .price .price-varies { color: var(--price_color); text-decoration: none; }
/*	.l4hs .price span { margin-right: 5px; }
    [dir="rtl"] .l4hs .price span { margin-right: 0; margin-left: 5px; }
    .l4hs .price .small { display: block; margin: 1px 0 2px; color: var(--primary_text_h); font-family: var(--main_ff); font-size: calc(var(--main_fz) * 0.8571428571); font-weight: var(--main_fw); font-size: 0.8571428571em; text-decoration: none; letter-spacing: var(--main_ls); opacity: .53; }
    .l4hs .price span[class*="overlay"] { display: block; margin: 0 0 4px; font-size: calc(var(--main_fz) * 0.8571428571); text-decoration: none; opacity: 1; }
    .l4hs .price .price-varies { color: var(--price_color); text-decoration: none; }*/
#root .l4hs p.submit {
	width: auto; margin-right: -8px;
	display: flex; flex-wrap: nowrap;
}
/*.l4hs .f8pr .submit:last-child, .l4hs .f8pr .link-btn:last-child, #root .l4hs .f8pr .submit.m10:last-child, #root .l4hs .f8pr .link-btn.m10:last-child, .l4hs .link-btn:last-child { margin-bottom: 0; }*/
.l4hs .f8pr .submit button, .l4hs .f8pr .link-btn a { min-width: 0; }
.l4hs .check { z-index: 999; margin-bottom: 2px; --check_color_size: 20px; --check_color_dist: 3px; --check_color_space: 6px; }
.l4hs .check.color { margin-bottom: -2px; }
.l4hs .check.color:not(:first-child) { margin-top: 2px; }
.l4hs figure .check.color { position: absolute; left: 0; right: 0; bottom: 0; z-index: 9; margin: 0; padding: calc(15px - var(--check_color_dist)) calc(10px - var(--check_color_dist) - var(--check_color_space)) calc(15px - var(--check_color_dist) - var(--check_color_space) * 0.5) calc(10px - var(--check_color_dist)); background: var(--body_bg); }
html:not(.mobile) .l4hs figure .check.color { visibility: hidden; opacity: 0; }
#root .l4hs .check li { width: auto; margin: 0; border-width: 0; }
#root .l4hs .check.color li { margin-right: var(--check_color_space); margin-bottom: var(--check_color_space); }
#root .l4hs input, #root .l4hs select, #root .l4hs button { border-radius: var(--b2r); }
#root .m6as.wide .l4hs figure img { border-radius: var(--b2p); }

figure .l4hs.static { position: relative; left: 0; right: 0; top: 0; bottom: 0; width: 100%; /*height: 100%;*/ padding: var(--rpp); }
#root figure .l4hs.static > li { position: relative; left: 0 !important; right: 0 !important; top: 0 !important; bottom: 0 !important; width: auto; height: auto; margin: 0; }
#root figure .l4hs.static > li > a { display: none; }
#root figure .l4hs.static > li > div { visibility: visible; position: relative; left: 0; right: 0; top: 0; bottom: 0; opacity: 1; }
#root figure .l4hs.static ~ picture { position: absolute; left: 0; right: 0; top: 0; bottom: 0; }

.l4hs.tabbed > li.toggle > a:before { content: ""; }

.l4hs > li > div, .l4hs > li > a, .l4hs > li > a:before { transition-property: all; transition-duration: 0.4s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: -.1s; }

/*@media only screen and (min-width: 1001px) {*/
#root .l4hs:not(.tabbed) > li.toggle > a:before { border-color: var(--bg_active); background: var(--bg_active); color: var(--fg_active); }
#root .l4hs:not(.tabbed, .ol) > li.toggle > a:before { transform: rotate(45deg); }
#root .l4hs:not(.tabbed) > li.toggle > a:after { --secondary_bg_btn: var(--bg_active); }
/*}*/
@media only screen and (max-width: 1000px) {
	#root .l4hs > li.toggle > div.desktop-only { display: none; }
}
@media only screen and (max-width: 760px) {
	#root .l4hs > li.toggle > div.desktop-only { display: none; }
	#root .l4hs > li > div { width: 100%; max-width: 294px; }
}