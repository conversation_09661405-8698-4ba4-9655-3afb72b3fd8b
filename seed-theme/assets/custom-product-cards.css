/* Custom Product Card Styles for Collection Pages */

/* Product Title Styling */
.custom-product-title {
  background-color: rgba(0, 0, 0, 0) !important;
  box-sizing: border-box !important;
  color: rgb(74, 74, 74) !important;
  cursor: pointer !important;
  display: inline !important;
  font-family: Lato, sans-serif !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  height: auto !important;
  line-height: 24px !important;
  outline-color: rgb(74, 74, 74) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  text-decoration-color: rgb(74, 74, 74) !important;
  text-decoration-line: none !important;
  text-decoration-style: solid !important;
  text-decoration-thickness: auto !important;
  text-size-adjust: 100% !important;
  width: auto !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
}

/* Price Container Layout */
.custom-price-container {
  display: flex !important;
  align-items: center !important;
  gap: 5px !important;
  flex-wrap: wrap !important;
  margin-top: 8px !important;
}

/* Sale Price Styling */
.custom-sale-price {
  box-sizing: border-box !important;
  color: rgb(252, 8, 8) !important;
  display: inline-block !important;
  font-family: Lato, sans-serif !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  line-height: 22.4px !important;
  margin-right: 5px !important;
  outline-color: rgb(252, 8, 8) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  text-size-adjust: 100% !important;
  text-transform: uppercase !important;
  unicode-bidi: isolate !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
}

/* Code Text Styling */
.custom-code-text {
  background-color: rgba(230, 245, 252, 0.5) !important;
  box-sizing: border-box !important;
  color: rgb(74, 74, 74) !important;
  display: inline-block !important;
  font-family: Lato, sans-serif !important;
  font-size: 15px !important;
  font-weight: 700 !important;
  line-height: 21px !important;
  outline-color: rgb(74, 74, 74) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  padding: 0px 1px !important;
  text-size-adjust: 100% !important;
  text-transform: uppercase !important;
  unicode-bidi: isolate !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
  border-radius: 2px !important;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .custom-product-title {
    font-size: 18px !important;
    line-height: 22px !important;
  }
  
  .custom-sale-price {
    font-size: 15px !important;
  }
  
  .custom-code-text {
    font-size: 14px !important;
    line-height: 19px !important;
  }
  
  .custom-price-container {
    gap: 3px !important;
  }
}

@media (max-width: 480px) {
  .custom-product-title {
    font-size: 16px !important;
    line-height: 20px !important;
  }
  
  .custom-sale-price {
    font-size: 14px !important;
  }
  
  .custom-code-text {
    font-size: 13px !important;
    line-height: 18px !important;
  }
}
