/* About Us Page Styles */

/* Global About Page Background */
.bg_page_about {
  position: relative;
  padding-top: 5px;
  overflow-x: hidden;
  overflow-y: hidden;
  /* background-color: #f9f9f9; */
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

/* Hero Section */
.about-hero-section {
  position: relative;
  z-index: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9 !important;
}

.about-hero-content {
  text-align: center;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.about-hero-title {
  font-family: Merriweather_, serif !important;
  font-size: 36px !important;
  font-weight: 400 !important;
  line-height: 50.4px !important;
  letter-spacing: 0.12px !important;
  color: #4a4a4a !important;
  margin: 0 auto 35px !important;
  max-width: 465px !important;
  width: 465px !important;
  text-align: center !important;
  box-sizing: border-box !important;
  display: block !important;
  height: 100.781px !important;
}

.about-hero-image-wrapper {
  width: 100%;
  max-width: 536px;
  height: 360px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-hero-image-wrapper img {
  width: 100%;
  max-width: 536px;
  height: 360px;
  object-fit: cover;
  border-radius: 8px;
}

.about-hero-placeholder {
  width: 100%;
  max-width: 536px;
  height: 360px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.meditation-illustration svg {
  width: 536px;
  height: 360px;
  border-radius: 8px;
}

/* Making Change Section */
.about-making-change-section {
  background-color: #fff;
  position: relative;
  z-index: 1;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  padding: 50px 0;
}

.about-making-change-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather_, serif;
  font-size: 36px;
  font-weight: 700;
  letter-spacing: 0.12px;
  line-height: 50.4px;
  margin-bottom: 90px;
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0px;
  text-align: center;
  width: 100%;
  max-width: 1180px;
  margin-left: auto;
  margin-right: auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-item {
  text-align: center;
  padding: 20px;
}

.feature-icon {
  margin: 0 auto 20px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-svg {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-svg svg {
  width: 80px;
  height: 80px;
  max-width: 80px;
  max-height: 80px;
}

.feature-icon svg {
  width: 80px;
  height: 80px;
}

.icon-placeholder {
  width: 80px;
  height: 80px;
  background-color: #f5f5f5;
  border: 2px dashed #cccccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-placeholder::before {
  content: "SVG";
  color: #999999;
  font-size: 14px;
  font-weight: 500;
}

.feature-description {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Lato_, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  margin-bottom: 0px;
  margin-left: 6.25px;
  margin-right: 6.25px;
  margin-top: 0px;
  max-width: 240px;
  text-align: center;
  width: 240px;
  margin-left: auto;
  margin-right: auto;
}

.icon-placeholder {
  width: 80px;
  height: 80px;
  background-color: #f5f5f5;
  border: 2px dashed #cccccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-placeholder::before {
  content: "Icon";
  color: #999999;
  font-size: 14px;
  font-weight: 500;
}

.feature-title {
  font-family: Lato_, sans-serif !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #4a4a4a !important;
  margin: 0 0 15px !important;
  line-height: 1.3 !important;
}

.feature-description {
  font-family: Lato_, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: #666 !important;
  margin: 0 !important;
}

/* Responsive Design */
@media only screen and (max-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media only screen and (max-width: 768px) {
  .bg_page_about {
    padding-top: 5px !important;
  }

  .about_bg {
    height: 300px;
  }

  .about-hero-content {
    padding: 0 15px;
  }

  .about-hero-title {
    font-size: 28px !important;
    line-height: 39.2px !important;
    margin-bottom: 25px !important;
    max-width: 100% !important;
    width: auto !important;
    height: auto !important;
  }

  .about-hero-image-wrapper {
    max-width: 100%;
    height: 250px;
  }

  .about-hero-placeholder {
    max-width: 100%;
    height: 250px;
  }

  .meditation-illustration svg {
    width: 100%;
    height: 250px;
    max-width: 400px;
  }

  .section-title {
    font-size: 28px;
    line-height: 38px;
    margin-bottom: 60px;
    padding: 0 20px;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    padding: 0 20px;
  }

  .feature-description {
    max-width: 200px;
    width: 200px;
    font-size: 14px;
    line-height: 20px;
  }

  .about-making-change-section {
    padding: 60px 0;
  }

  .about-making-change-content {
    padding: 0 15px;
  }

  .section-title {
    font-size: 28px;
    margin-bottom: 40px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .feature-item {
    padding: 15px;
  }

  .feature-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 15px;
  }
}

@media only screen and (max-width: 480px) {
  .bg_page_about {
    padding-top: 5px !important;
  }

  .about-hero-title {
    font-size: 24px !important;
    line-height: 33.6px !important;
  }

  .about-hero-image-wrapper {
    height: 200px;
  }

  .about-hero-placeholder {
    height: 200px;
  }

  .meditation-illustration svg {
    height: 200px;
    max-width: 320px;
  }

  .section-title {
    font-size: 24px;
    line-height: 32px;
    margin-bottom: 50px;
    padding: 0 15px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 15px;
  }

  .feature-description {
    max-width: 280px;
    width: 280px;
    font-size: 14px;
    line-height: 19px;
  }

  .section-title {
    font-size: 24px;
    margin-bottom: 30px;
  }

  .feature-title {
    font-size: 16px !important;
  }

  .feature-description {
    font-size: 13px !important;
  }
}

/* Animation for page load */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.about-hero-content {
  animation: fadeInUp 0.8s ease-out;
}

.feature-item {
  animation: fadeInUp 0.8s ease-out;
}

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-item:nth-child(4) { animation-delay: 0.4s; }

/* Accessibility improvements */
.feature-item:focus-within {
  outline: 2px solid #4ECDC4;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .about-hero-section,
  .about-making-change-section {
    break-inside: avoid;
  }
  
  .feature-item {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
