#root .l4ml li:before { content: ""; display: block; overflow: hidden; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: -1; margin: 0; font-size: var(--main_fz); text-align: left; text-indent: -3000em; direction: ltr; }
.l4ml li > span:first-child, .l4ml li > figure:first-child + * { overflow-wrap: break-word; -ms-word-break: break-word; word-break: break-word; -webkit-hyphens: auto; /*-webkit-hyphenate-limit-before: 3; -webkit-hyphenate-limit-after: 3; -webkit-hyphenate-limit-chars: 6 3 3; -webkit-hyphenate-limit-lines: 2; -webkit-hyphenate-limit-last: always; -webkit-hyphenate-limit-zone: 8%; -moz-hyphens: auto; -moz-hyphenate-limit-chars: 6 3 3; -moz-hyphenate-limit-lines: 2; -moz-hyphenate-limit-last: always; -moz-hyphenate-limit-zone: 8%;*/ -ms-hyphens: auto; /*-ms-hyphenate-limit-chars: 6 3 3; -ms-hyphenate-limit-lines: 2; -ms-hyphenate-limit-last: always; -ms-hyphenate-limit-zone: 8%;*/ hyphens: auto; /*hyphenate-limit-chars: 6 3 3; hyphenate-limit-lines: 2; hyphenate-limit-last: always; hyphenate-limit-zone: 8%;*/ }

#root .l4ml { overflow: visible; list-style: none; width: calc(100% + var(--pt)); margin: calc(0px - var(--pt)) calc(0px - var(--pt)) var(--main_mr) 0; padding: 0; font-weight: var(--main_fw); font-size: var(--main_fz); line-height: calc(var(--main_lh) * 0.425 + var(--main_lh_h) * 0.425); text-align: var(--text_align_start); opacity: 1; --w: 100%; --imgw: 50px; --pt: 16px; }
	#root .l4ml li { position: relative; z-index: 3; width: var(--w); margin: 0; padding: var(--pt) 0; border-width: 0; }
		#root .l4ml li:before { right: var(--pt); bottom: -1px; width: auto; border: 0 solid var(--custom_bd); border-top-width: 1px; /*border-bottom-width: 1px;*/ opacity: 1; }
		#root .l4ml li:first-child:before { border-top-width: 0; }
		.l4ml li.disabled { pointer-events: none; }
		.l4ml li.disabled > *:not(:first-child, figure:first-child + span), .l4ml li.disabled .s1pr { -webkit-filter: blur(3px); filter: blur(3px); }
	#root .l4ml li.has-l4ca { display: none; padding-top: 0; }
	/*#root .l4ml li:has(.l4ca) { display: none; padding-top: 0; }*/
	#root .l4ml li.toggle-l4ca + li.has-l4ca { display: block; }
	/*#root .l4ml li.toggle-l4ca + li:has(.l4ca) { display: block; }*/
	#root .l4ml li.link-more { width: 100%; padding-top: 12px; padding-bottom: 12px; line-height: var(--main_lh); }
		#root .l4ml li.link-more:last-child, #root .l4ml li.link-more.last-child { margin-bottom: 0; padding-bottom: 0; }
		#root .l4ml li.link-more:last-child:before, #root .l4ml li.link-more.last-child:before { border-bottom-width: 0; }
		#root .l4ml a.link-more { color: var(--secondary_bg); }
	.l4ml a { display: inline; }
	.l4ml a, .l4ml a.remove { color: inherit; }
	.l4ml a:not(.text-underline), .l4ml a.remove { text-decoration: none; }
	.l4ml a:has(.icon-trash) { color: inherit; text-decoration: none; }
	.l4ml .icon-trash, .l4ml [class*="icon-x"] { display: block; position: relative; top: -.05em; color: inherit; font-size: 17px; opacity: .62; }
		.l4ml .icon-trash:before { content: "\e93d"; }
		.l4ml [class*="icon-x"] { font-size: 22px; opacity: .34; }
		[data-whatintent="mouse"] .l4ml a:hover .icon-trash, [data-whatintent="mouse"] .l4ml a:hover [class*="icon-x"] { color: var(--secondary_bg); opacity: 1; }
		[data-whatintent="mouse"] .l4ml a:hover .icon-trash:before { content: "\e93a"; }
	#root .l4ml li > * { display: block; position: relative; z-index: 2; margin: 0 var(--pt) 0 0; padding: 0; }
		.l4ml li > span:first-child, .l4ml li > figure:first-child + * { min-width: 0; }
		.l4ml li > span:first-child ~ span, .l4ml li > figure:first-child + span ~ span, .l4ml li > figure { flex-shrink: 0; }
		.l4ml li > span:first-child, .l4ml li > figure:first-child + span { flex-grow: 3; }
		[dir="ltr"] #root .l4ml li > span:first-child, [dir="ltr"] #root .l4ml li > figure:first-child + span { margin-right: auto; padding-right: var(--pt); }
		[dir="rtl"] #root .l4ml li > span:first-child, [dir="rtl"] #root .l4ml li > figure:first-child + span { margin-left: auto; }
		#root .l4ml li > figure, #root .l4cl.list .l4ml li > figure { 
			float: none; width: 50px; margin-left: 0; margin-right: var(--pt);
			align-self: center; 
		}
			#root .l4ml li > figure ~ * { float: none; width: auto; }
			.l4ml li > figure picture { display: block; width: 100%; }
			#root .l4ml li > figure picture { opacity: 1 !important; }
			.l4ml figure.portrait picture, .l4ml figure.landscape picture, .l4ml figure[style*="--ratio:"] picture { padding-top: calc(var(--ratio) * 100%); }
			.l4ml figure.portrait picture img, .l4ml figure.landscape picture img, .l4ml figure[style*="--ratio:"] picture img { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; width: 100% !important; height: 100% !important; }
	.l4ml .s1tt { z-index: 9; pointer-events: auto; }
	.l4ml .s1pr { margin: 0; /*line-height: inherit;*/ }
		.l4ml li > .s1pr { text-align: var(--text_align_end); /*white-space: pre-wrap;*/ }
		#root .l4ml .s1pr > * { margin-left: 0; margin-right: 0; }
		.l4ml .size-12 + .s1pr { display: block; margin-top: 2px; }
	#root .l4ca .l4ml:last-child { margin-bottom: 0; }
		#root .l4ca .l4ml:last-child li.link-more:last-child, #root .l4ca .l4ml:last-child li.link-more.last-child { margin-bottom: 13px; }
		/*#root .l4ca .l4ml > *:last-child:before, #root .l4ca .l4ml > .last-child:before { border-bottom-width: 0; }*/
	#root .l4ml-form { display: block; padding: 0; border-width: 0; }
		#root .l4ml-form:before, .l4ml a.remove .was-hidden { display: none; }
	#root .box-inset:has([class*="l4ml"]) { display: block; }
	#root .box-inset.has-l4ml { display: block; }
	#root .l4ml + * { margin-top: 0; padding-top: 0; }
	.text-center .l4ml-form .submit { justify-content: flex-start; }
	.l4ml-form .sticky { display: block; margin-bottom: var(--main_mr_half); font-size: var(--main_fz_small); line-height: var(--main_lh_l); text-align: var(--text_align_end); }
		.l4ml-form .sticky .s1tt { left: 0; right: 0; margin-left: 4px; margin-right: 0; }
		[dir="rtl"] .l4ml-form .sticky .s1tt { margin-left: 0; margin-right: 4px; }
	.l4ml + hr { margin-top: calc(0px - var(--main_mr)); }
	#root .l4ca .l4ml .s1pr { width: auto; }
	
/*.l4cl {}*/
	#root .l4cl .l4ml-form { position: relative; z-index: 9; margin-top: var(--main_mr); text-align: var(--text_align_start); }
	.l4cl .l4ml-form button { flex-grow: 3; }
	#root .l4cl.list .l4ml-form { width: calc(440px + var(--pr_dist)); }
	#root .l4cl .l4ml figure ~ * { float: none; clear: none; width: auto; }
	.l4cl.list li:has(.l4ml-form) > div { align-self: flex-start; }

/*.l4ca {}*/
	.l4ca li.has-l4ml { padding-top: 0; padding-bottom: 0; }
	.l4ca li:has([class*="l4ml"]) { padding-top: 0; padding-bottom: 0; }
	.l4ca li.has-l4ml:before { border-top-width: 0; }
	.l4ca li:has([class*="l4ml"]):before { border-top-width: 0; }
	/*#root .l4ca li:has([class*="l4ml"]):before { border-bottom-width: 0; }
	#root .l4ca li.has-l4ml:before { border-bottom-width: 0; }*/
	/*#root .l4ca li:has([class*="l4ml"]) + li:before { border-top-width: 0; }
	#root .l4ca li.has-l4ml + li:before { border-top-width: 0; }*/
	
.product-scrolled.scrolled.has-f8ps.m6cp-open { --root_pb: 0px; }

.m6cp { 
	display: block; position: fixed; bottom: 0; z-index: 9; width: 240px; --pdi: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh));
	transform: translateY(100%);
}
	#root .m6cp { margin-bottom: 0; }
	.m6cp > header { margin-top: calc(0px - var(--btn_pv) * 2 - var(--btn_fz) * var(--btn_lh)); }
	.m6cp > header .link-btn, .m6cp > header .link-btn > * { margin-bottom: 0; }
	.m6cp > header .link-btn > a { overflow: hidden; height: calc(0px - var(--btn_pv) * 2 - var(--btn_fz) * var(--btn_lh)); text-align: var(--text_align_start); text-overflow: ellipsis; white-space: nowrap; --btn_br: var(--b2p); }
		[dir="ltr"] .m6cp > header .link-btn > a { padding-right: var(--pdi); }
		[dir="rtl"] .m6cp > header .link-btn > a { padding-left: var(--pdi); }
		.m6cp > header .link-btn > a [class*="icon-chevron-"] { display: block; position: absolute; left: var(--lar0); right: var(--l0ra); top: 50%; width: var(--pdi); margin-top: -10px; line-height: 20px; text-align: center; }
		.m6cp > header .link-btn > a, .m6cp > header .link-btn > a:before { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
	#root .m6cp:not(.loading) > div > .link-btn, #root .m6cp.loading > div > *:not(.link-btn) { display: none; }
	.product-scrolled.scrolled:not(.m6cp-open):has(.f8ps:not(.align-top)) .m6cp { bottom: var(--f8ps_h); }
.m6cp, [dir="rtl"] .m6cp.align-start { left: auto; right: var(--rpp); }
[dir="rtl"] .m6cp, .m6cp.align-start { left: var(--rpp); right: auto; }	
.m6cp.inactive { visibility: hidden; bottom: -10px; opacity: 0; }
	#root:has(.m6cp:not(.inactive, .align-start)) #totop { left: var(--l0ra); right: var(--lar0); }

.table-compare { table-layout: fixed; --th_w: calc(150px - var(--p_lr)); --td_w: auto; --main_mr: 18px; --p_lr: 18px; }
	.table-compare .l4cl:last-child, .table-compare .l4cl > *:last-child, .table-compare tr > * > *:last-child { margin-bottom: 0; }
	.table-compare tr > * > .check.color:last-child { margin-bottom: calc(0px - var(--check_color_space)); }
	#root .table-compare tr > * { width: var(--td_w); min-width: var(--td_w); max-width: var(--td_w); border-left-width: 0; border-right-width: 0; border-bottom-width: 1px; background: none; vertical-align: top; }
		[dir="ltr"] .table-compare tr > *, [dir="rtl"] .table-compare tr > *:first-child { padding-right: 0; }
		[dir="rtl"] .table-compare tr > *, [dir="ltr"] .table-compare tr > *:first-child { padding-left: 0; }
		#root .table-compare tr:first-child > * { padding-top: 0; border-top-width: 0; }
		#root .table-compare tr:first-child:not(:last-child) > * { border-bottom-width: 0; }
		#root .table-compare tr:first-child + tr > * { padding-top: 0; }
	#root .table-compare tr > th:first-child { width: var(--th_w); min-width: var(--th_w); max-width: var(--th_w); }
	.table-compare h1, .table-compare h2, .table-compare h3, .table-compare h4, .table-compare h5, .table-compare h6 { font-size: 1em; }
	.table-compare .check { z-index: 98; }
	.table-compare th { overflow-wrap: break-word; -ms-word-break: break-word; word-break: break-word; -webkit-hyphens: auto; /*-webkit-hyphenate-limit-before: 3; -webkit-hyphenate-limit-after: 3; -webkit-hyphenate-limit-chars: 6 3 3; -webkit-hyphenate-limit-lines: 2; -webkit-hyphenate-limit-last: always; -webkit-hyphenate-limit-zone: 8%; -moz-hyphens: auto; -moz-hyphenate-limit-chars: 6 3 3; -moz-hyphenate-limit-lines: 2; -moz-hyphenate-limit-last: always; -moz-hyphenate-limit-zone: 8%;*/ -ms-hyphens: auto; /*-ms-hyphenate-limit-chars: 6 3 3; -ms-hyphenate-limit-lines: 2; -ms-hyphenate-limit-last: always; -ms-hyphenate-limit-zone: 8%;*/ hyphens: auto; /*hyphenate-limit-chars: 6 3 3; hyphenate-limit-lines: 2; hyphenate-limit-last: always; hyphenate-limit-zone: 8%;*/ }
	/*.table-compare .check { --check_color_size: 20px; --check_color_space: 5px; }*/
	.table-compare .check { --check_color_size: 20px; --check_color_dist: 3px; --check_color_space: 6px; }
	.table-compare .check.rect { --check_color_size: 32px; }
		
#root .l4cl .l4ml-form:first-child, #root .l4cl.list .l4ml-form { margin-top: 0; }
	.l4cl.list .l4ml-form { align-self: flex-start; }
	
.l4ml br:last-child, .l4ml label.was-hidden, #root .l4ml li.hidden, #root .l4ml .removed, .m6cp.hidden { display: none; }
	
#root .l4ml, #root .l4ml li { display: flex; flex-wrap: wrap; }
#root .l4ml li { flex-direction: row; }
#root .l4ml li { flex-wrap: nowrap; }
#root .l4ml li { justify-content: flex-end; }
#root .l4ml li { align-items: center; }

@media only screen and (min-width: 1001px) and (max-width: 1100px) {
/*.m6cl .l4cl.list {}*/
	.m6cl .l4cl.list li.has-l4ml { flex-wrap: wrap; }
	.m6cl .l4cl.list li:has([class*="l4ml"]) { flex-wrap: wrap; }
		.m6cl .l4cl.list li.has-l4ml > div:not(:last-child) { width: calc(100% - var(--img_w) - var(--img_dist)); min-width: calc(100% - var(--img_w) - var(--img_dist)); flex-basis: auto; }
		.m6cl .l4cl.list li:has([class*="l4ml"]) > div:not(:last-child) { width: calc(100% - var(--img_w) - var(--img_dist)); min-width: calc(100% - var(--img_w) - var(--img_dist)); flex-basis: auto; }
	#root .m6cl .l4cl.list li > .l4ml-form { position: relative; z-index: 9; clear: both; width: 100%; min-width: 0; max-width: none; padding: var(--img_dist) 0 0; text-align: var(--text_align_start); }
		#root .m6cl .l4cl.list li > .l4ml-form .sticky:first-child, #root .m6cl .l4cl.list li > .l4ml-form legend + .sticky { margin-top: calc(0px - var(--main_mr)); }
}
@media only screen and (min-width: 1001px) {
/*.l4cl {}*/
	[dir="ltr"] #root .l4cl.list .l4ml-form { padding-left: var(--pr_dist); }
	[dir="rtl"] #root .l4cl.list .l4ml-form { padding-right: var(--pr_dist); }
}
@media only screen and (max-width: 1000px) {
/*.l4cl.list {}*/
	.l4cl.list li.has-l4ml { flex-wrap: wrap; }
	.l4cl.list li:has([class*="l4ml"]) { flex-wrap: wrap; }
		.l4cl.list li.has-l4ml > div:not(:last-child) { flex-basis: auto; }
		.l4cl.list li:has([class*="l4ml"]) > div:not(:last-child) { flex-basis: auto; }
	#root .l4cl .l4ml-form { position: relative; z-index: 9; margin-top: var(--main_mr); text-align: var(--text_align_start); }
	#root .l4cl.list li.has-l4ml > figure { margin-bottom: var(--img_dist); }
	#root .l4cl.list li:has([class*="l4ml"]) > figure { margin-bottom: var(--img_dist); }
	#root .l4cl.list li > .l4ml-form { clear: both; width: 100%; min-width: 0; max-width: none; /*padding-top: var(--img_dist);*/ }
		#root .l4cl.list li > .l4ml-form .sticky:first-child, #root .l4cl.list li > .l4ml-form legend + .sticky { margin-top: calc(0px - var(--main_mr)); }
.m6cp { --btn_ph: var(--rpp); }
.table-compare { --th_w: 95px; }
}
@media only screen and (min-width: 761px) { /* 760 */
/*.m6cp {}*/
	.m6cp > header .link-btn > a { width: 100%; }
#root .l4ml.w50 { --w: 50%; }
	#root .l4ml.w50 li:first-child + li:before { border-top-width: 0; }
	#root .l4ml.w50 li.link-more:before { border-bottom-width: 0; }
/*.table-compare {}*/
	.table-wrapper:has(.table-compare) { overflow: visible; width: auto; margin-left: 0; margin-right: 0; }
}
@media only screen and (max-width: 760px) { /* 760 */
#root .l4ml { --imgw: 44px; }
	#root .l4ca:not(.compact) .l4ml { margin-top: 0; }
	#root .l4ca:not(.compact) .l4ml > li:first-child { margin-top: calc(0px - var(--pt)); } 
	#root .m6cl .l4cl .l4ml li { border-left-width: 0; }
.m6cp-open #root .m6cp { left: 0; right: 0; width: 100%; }
	.m6cp-open:not([data-theme="xpert"]) .m6cp > header .link-btn a, .m6cp-open:not([data-theme="xpert"]) .m6cp > header .link-btn a:before { border-radius: 0; }
	[data-theme="xpert"].m6cp-open #root .m6cp { left: 12px; right: 12px; bottom: 12px; width: calc(100% - 24px); }
		.m6cp-open .m6cp > header .link-btn > a { width: 100%; } 
	[data-theme="xpert"] .m6cp, [data-theme="xpert"] .m6cp > footer { border-bottom-left-radius: var(--b2p); border-bottom-right-radius: var(--b2p); }
	.m6cp > header .link-btn > a { padding-left: min(var(--rpp), var(--btn_ph)); padding-right: min(var(--rpp), var(--btn_ph)); }
.table-compare { --td_w: max(calc(100vw - var(--th_w) - var(--rpp) * 4), 160px); }
	.table-wrapper .table-compare { margin-left: var(--rpp); margin-right: var(--rpp); margin-top: 10px; }
#root .compare-1 .table-wrapper:has(.table-compare) { overflow: visible; width: auto; margin-left: 0; margin-right: 0; }
	#root .compare-1 .table-wrapper:has(.table-compare) .table-compare { margin-left: 0; margin-right: 0; }
	#root .compare-1 .table-compare tr > *:not(:first-child) { width: auto; min-width: 0; max-width: none; }
	
#root .l4ml li > .mobile-hide { display: none; }
}

