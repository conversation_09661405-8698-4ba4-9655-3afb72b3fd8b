:root {
 --input_valid_check: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10.9 9'%3E%3Cpath d='M4.6 8.9c-.1.1-.2.1-.3 0l-4.2-4c-.1-.1-.1-.2 0-.3l1.3-1.4c.1-.2.3-.2.4-.1l2.3 2.2c.1.1.3.1.4 0L9 .1c0-.1.2-.1.3 0l1.5 1.3c.1.1.1.3 0 .4L4.6 8.9z' fill='%2395bf47'/%3E%3C/svg%3e");
}
/*.is-invalid { }*/
	.invalid-feedback { display: none; margin-top: 6px; color: var(--alert_error); font-size: calc(var(--main_fz) * 0.8571428571); }
	.is-invalid input, .is-invalid select, .is-invalid textarea, .is-invalid .check label:before, .is-invalid.check label:before, #search .is-invalid input, .is-invalid .bv_atual { border-color: var(--alert_error); background-color: var(--alert_error_bg); }
		.is-invalid .bv_atual:before { color: var(--alert_error); }
		.is-invalid input.empty, .is-invalid textarea.empty, #search .is-invalid input.empty { border-color: var(--custom_input_bd); background-color: var(--white); }
	.check.inline > .invalid-feedback { width: 100%; }
			.is-invalid input ~ .size-12, .is-invalid select ~ .size-12, .is-invalid textarea ~ .size-12, .is-invalid .bv_atual ~ .size-12, .is-invalid .select-wrapper ~ .size-12 { margin-top: 0; }
	#root is-invalid input[type="date"], #root is-invalid .datepicker-input { background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xml:space='preserve' style='enable-background:new 0 0 14 16' viewBox='0 0 14 16' fill='%23eb5757'%3E%3Cpath d='M12.3 2H11V.8c0-.5-.3-.8-.7-.8s-.8.3-.8.8V2h-5V.8c0-.5-.3-.8-.7-.8S3 .3 3 .8V2H1.8C.8 2 0 2.8 0 3.8v10.5c0 1 .8 1.8 1.8 1.8h10.5c1 0 1.8-.8 1.8-1.8V3.8c-.1-1-.9-1.8-1.8-1.8zm.2 12.3c0 .1-.1.3-.3.3H1.8c-.1 0-.3-.1-.3-.3V7.5h11v6.8zm0-8.3h-11V3.8c0-.1.1-.3.3-.3h10.5c.1 0 .3.1.3.3V6z'/%3E%3C/svg%3E"); background-position: calc(100% - var(--main_fz)) center; background-size: auto max(18px, calc(var(--input_h) - var(--main_fz) * 2)); line-height: normal; } 
/*.is-valid { }*/
	.is-valid input, .is-valid select { padding-right: 45px; background-position: calc(100% - 15px) center; background-image: var(--input_valid_check); background-size: auto 9px; }
	.is-valid .has-show ~ input { background-position: -3000em -3000em; }
	.is-valid input:placeholder-shown { padding-right: 15px; padding-right: 15px; background-position: -3000em -3000em; }
	
.input-amount.is-invalid .incr, .input-amount.is-invalid .decr { color: var(--alert_error); }
			
.is-invalid .empty ~ .invalid-feedback { display: none; }
.is-invalid .invalid-feedback { display: block; }

[data-theme="xpert"] {
 --input_valid_check: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xml:space='preserve' style='enable-background:new 0 0 11 8.3' viewBox='0 0 11 8.3'%3E%3Cpath d='M10.7.3c.4.4.4 1 0 1.5L4.7 8c-.3.3-.9.3-1.2 0L.3 4.7c-.4-.4-.4-1 0-1.5.4-.4 1-.4 1.4 0l2.4 2.4L9.3.2c.4-.3 1-.3 1.4.1z' style='fill:%236eb554'/%3E%3C/svg%3E");
}
		

/*form.processing .submit {}*/
	#root form.processing:not([data-hold]) .submit [type="button"]:not([class*="shopify-payment-button"]), #root form.processing:not([data-hold]) .submit [type="submit"]:not([class*="shopify-payment-button"]), #root form.processing:not([data-hold]) .submit [type="button"]:not([class*="shopify-payment-button"]) *, #root form.processing:not([data-hold]) .submit [type="submit"]:not([class*="shopify-payment-button"]) *, #search.processing button:before { color: rgba(0,0,0,0); }
	#root form.processing .submit [type="button"], #root form.processing .submit [type="submit"], #search.processing button { pointer-events: none; }
	#root form.processing .submit [type="button"]:not([class*="shopify-payment-button"]):after, #root form.processing .submit [type="submit"]:not([class*="shopify-payment-button"]):after, #search.processing button:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 18px; height: 18px; margin: -9px 0 0 -9px; border-radius: 20px; border: 2px solid var(--secondary_btn_text); border-left-color: rgba(0,0,0,0) !important; pointer-events: none; }
	#root form.processing .submit .inv[type="button"]:not([class*="shopify-payment-button"]):after, #root form.processing .submit .inv[type="submit"]:not([class*="shopify-payment-button"]):after, #search.processing button.inv:after,
	#root form.processing .submit .inv-btn [type="button"]:not([class*="shopify-payment-button"]):after, #root form.processing .submit .inv-btn [type="submit"]:not([class*="shopify-payment-button"]):after, #search.processing .inv-btn button:after { border-color: var(--secondary_bg_btn); }
		#root form.processing .submit [type="button"]:not([class*="shopify-payment-button"]):after, #root form.processing .submit [type="submit"]:not([class*="shopify-payment-button"]):after, #search.processing button:after { -webkit-transition: none; transition: none; }		
		
	#root form.processing .submit [type="button"]:after, #root form.processing .submit [type="submit"]:after, #search.processing button:after { animation-name: spin; animation-duration: .75s; animation-fill-mode: forwards; animation-iteration-count: infinite; animation-timing-function: linear; }
		#search.processing button:after { border-color: var(--custom_top_search_fg); border-left-color: rgba(0,0,0,0); }
		#search.processing button:after { width: 16px; height: 16px; margin-top: -8px; }
	form.processing .submit [type="reset"] { z-index: 9; }
	
#root form[data-hold].processing .submit [type="button"]:after, #root form[data-hold].processing .submit [type="submit"]:after, #root form[data-hold].processing .link-btn [type="button"]:after, #root form[data-hold].processing .link-btn [type="submit"]:after { content: ""; display: block; position: absolute; left: auto; right: 0; top: 0; bottom: 0; width: 100%; height: auto; margin: 0; border-radius: 0; border-width: 0; background: #fff; opacity: .2; }
	#root form[data-hold].processing .submit [type="button"].inv:after, #root form[data-hold].processing .submit [type="submit"].inv:after, #root form[data-hold].processing .link-btn [type="button"].inv:after, #root form[data-hold].processing .link-btn [type="submit"].inv:after { background: var(--secondary_bg); }
	#root form[data-hold].processing .submit [type="button"].inv.overlay-tertiary:after, #root form[data-hold].processing .submit [type="submit"].inv.overlay-tertiary:after, #root form[data-hold].processing .link-btn [type="button"].inv.overlay-tertiary:after, #root form[data-hold].processing .link-btn [type="submit"].inv.overlay-tertiary:after { background: var(--tertiary_bg); }
	#root form[data-hold].processing .submit [type="button"].inv.overlay-content:after, #root form[data-hold].processing .submit [type="submit"].inv.overlay-content:after, #root form[data-hold].processing .link-btn [type="button"].inv.overlay-content:after, #root form[data-hold].processing .link-btn [type="submit"].inv.overlay-content:after { background: var(--gray_text); }
	#root form[data-hold].processing .submit [type="button"].inv.overlay-coal:after, #root form[data-hold].processing .submit [type="submit"].inv.overlay-coal:after, #root form[data-hold].processing .link-btn [type="button"].inv.overlay-coal:after, #root form[data-hold].processing .link-btn [type="submit"].inv.overlay-coal:after { background: var(--primary_text); }
#root form[data-hold].processing .submit [type="button"]:after, #root form[data-hold].processing .submit [type="submit"]:after, #root form[data-hold].processing .link-btn [type="button"]:after, #root form[data-hold].processing .link-btn [type="submit"]:after { animation-name: shrink_width; animation-duration: 2000ms; animation-iteration-count: 1; }
[dir="rtl"] #root form[data-hold].processing .submit [type="button"]:after, [dir="rtl"] #root form[data-hold].processing .submit [type="submit"]:after { left: 0; right: auto; }


@keyframes spin { 0% { transform: none; } 100% { transform: rotate(360deg); } }
@keyframes shrink_width { 0% { width: 100%; } 100% { width: 0%; } }