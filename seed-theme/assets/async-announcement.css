/* Removed :after pseudo-element to prevent flickering */
.shopify-section-announcement-bar a, [data-whatintent="mouse"] .shopify-section-announcement-bar a:hover { color: inherit; }
.shopify-section-announcement-bar a.close:before { display: block; overflow: visible; position: absolute; left: 0; right: 0; top: 50%; bottom: auto; margin: -10px 0 0; box-shadow: none; border-radius: 0; border-width: 0; background: none; font-weight: 400; font-family: i; font-style: normal; line-height: 20px; text-align: center; text-indent: 0; letter-spacing: normal; }
			
.shopify-section-announcement-bar .size-m { --ann_p: 19px; }
.shopify-section-announcement-bar .size-l { --ann_p: 24px; }
	.shopify-section-announcement-bar { display: none; overflow: hidden; position: relative; z-index: 9; margin: 0 0 -.1px; padding: 0 var(--rpp); background: var(--custom_alert_bg); color: var(--custom_alert_fg); font-size: var(--main_fz); line-height: var(--main_lh_h); --ar_c: var(--custom_alert_fg); width: 100vw; left: 50%; right: 50%; margin-left: -50vw; margin-right: -50vw; }
		.shopify-section-announcement-bar > * ~ * { display: none; }
	#root > [class*="shopify-section-announcement"] { position: relative; z-index: 9; width: calc(100% + var(--rpp) * 2); margin-left: var(--rpn); margin-right: var(--rpn); background: var(--body_bg); color: var(--primary_text); --ar_c: var(--primary_text); }
	.shopify-section-announcement-bar.s4wi { padding-left: 0; padding-right: 0; }
		.shopify-section-announcement-bar p { max-width: var(--glw); margin-left: auto; margin-right: auto; padding-right: 24px; }
			.shopify-section-announcement-bar.s4wi p { padding-left: 0; padding-right: 0; }
			.shopify-section-announcement-bar.text-center p, .shopify-section-announcement-bar .text-center, p .shopify-section-announcement-bar p.text-center { padding-left: 16px; padding-right: 16px; }
			.shopify-section-announcement-bar .text-center p { padding-left: 0; padding-right: 0; }
		.shopify-section-announcement-bar .swiper-slide { overflow: hidden; padding-left: var(--rpp); padding-right: var(--rpp); }
			/* Removed p:before pseudo-element to prevent flickering */
		.shopify-section-announcement-bar > p { padding-bottom: var(--ann_p); }
		.shopify-section-announcement-bar > p:first-child { padding-top: var(--ann_p); }
		.shopify-section-announcement-bar.m6kn, .shopify-section-announcement-bar .m6kn, .shopify-section-announcement-bar .m6kn ul { margin: 0; }
		.shopify-section-announcement-bar.m6kn li { padding-top: var(--ann_p); padding-bottom: var(--ann_p); }
		/*.shopify-section-announcement-bar.m6kn.dot:not(.type) li:after { padding-top: var(--ann_p); }*/
		.shopify-section-announcement-bar.m6kn .close { display: none; }
		.shopify-section-announcement-bar > div p { padding-top: var(--ann_p); padding-bottom: var(--ann_p); }
		.shopify-section-announcement-bar > .swiper-custom-pagination { display: none; }
		.shopify-section-announcement-bar ~ #root { min-height: calc(100vh - 44px); }
		.shopify-section-announcement-bar a.close { display: block; position: absolute; right: 0; top: 0; width: 44px; height: 100%; margin: 0; padding: 0; color: inherit; font-size: var(--size_12); text-indent: -3000em; text-align: left; direction: ltr; }
			.shopify-section-announcement-bar a.close:before { content: "\e91f"; }
			.shopify-section-announcement-bar a.close:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 44px; height: 44px; margin: -22px 0 0 -22px; }
			.shopify-section-announcement-bar.s4wi a.close { display: none; }
		.shopify-section-announcement-bar * { margin: 0; }
		.shopify-section-announcement-bar .cols { margin-left: calc(0px - var(--cols)); }
		.shopify-section-announcement-bar .swiper-button-next { right: 0; }
		.shopify-section-announcement-bar .swiper-button-prev { left: 0; }
			.shopify-section-announcement-bar .swiper-button-next, .shopify-section-announcement-bar .swiper-button-prev { display: block; width: var(--rpp); color: var(--ar_c); }
			.shopify-section-announcement-bar .swiper-button-next:before, .shopify-section-announcement-bar .swiper-button-prev:before { color: var(--ar_c); }

.shopify-section-announcement-bar .overlay-close { display: block; z-index: 20; }
.m6pn-open .shopify-section-announcement-bar > .overlay-close, .m6cp-open .shopify-section-announcement-bar > .overlay-close { visibility: visible; opacity: 1; }
.m6pn-open:has(.m6pn.no-overlay.toggle) .shopify-section-announcement-bar > .overlay-close, .m6pn-open:has(.m6pn.no-overlay.toggle) .shopify-section-announcement-bar > .overlay-close { visibility: hidden; opacity: 0; }

.shopify-section-announcement-bar .text-center .cols, .shopify-section-announcement-bar.text-center .cols { justify-content: center; }
.shopify-section-announcement-bar .cols { justify-content: flex-start; }
.shopify-section-announcement-bar .cols { align-items: center; }

.m6pn-open #root .shopify-section-announcement-bar > .overlay-close { opacity: 0; }

.t1as .shopify-section-announcement-bar, .show-notice.t1as .shopify-section-announcement-bar, .shopify-section-announcement-bar .mobile-only, .shopify-section-announcement-bar.no-nav .swiper-button-prev, .shopify-section-announcement-bar.no-nav .swiper-button-next, .shopify-section-announcement-bar .no-nav .swiper-button-prev, .shopify-section-announcement-bar .no-nav .swiper-button-next { display: none; }
.shopify-section-announcement-bar > .overlay-close ~ .overlay-close { display: none !important; }
.show-notice .shopify-section-announcement-bar { display: block; }



@media only screen and (max-width: 1300px) { /* 1300 */
/*.shopify-section-announcement-bar {}*/
	.shopify-section-announcement-bar .swiper-slide { padding-left: 44px; padding-right: 44px; }
	.shopify-section-announcement-bar.no-nav .swiper-slide, .shopify-section-announcement-bar .no-nav .swiper-slide { padding-left: var(--rpp); padding-right: var(--rpp); }
	.shopify-section-announcement-bar .swiper-button-next, .shopify-section-announcement-bar .swiper-button-prev { width: 44px; }
	.shopify-section-announcement-bar .swiper-button-next:before { left: auto; right: var(--rpp); }
	.shopify-section-announcement-bar .swiper-button-prev:before { right: auto; left: var(--rpp); }
}


@media only screen and (max-width: 760px) { /* 760 */
.shopify-section-announcement-bar ~ #root { overflow: visible; scrollbar-width: none; }
.shopify-section-announcement-bar ~ #root::-webkit-scrollbar { width: 0; }

.shopify-section-announcement-bar .mobile-only { display: block; }
.shopify-section-announcement-bar span.mobile-only { display: inline; }
.shopify-section-announcement-bar .mobile-hide { display: none; } 
}
