:root {
	--range_bg: var(--secondary_bg);
	--range_fg: var(--primary_text);
	--range_bg_blank: var(--custom_bd);
	--range_bg_blank_op: 1;
	--range_tip_bg: var(--secondary_bg); 
	--range_tip_fg: var(--white);
}
.input-range.slider-is-here:not(.single) { position: relative; z-index: 2; padding-top: 40px; }
	.input-range.slider-is-here:not(.single) .range-inner { position: absolute; left: var(--d); right: var(--d); top: 0; width: auto; margin: 0; }
	.input-range.slider-is-here.single input { display: none; }
	.input-range:first-child, .hidden:first-child + .input-range { margin-top: 0; }

.input-range, .input-range-steps { --d: 11px; }
.noUi-target, .noUi-target * { -webkit-touch-callout: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); -webkit-user-select: none; -ms-touch-action: none; touch-action: none; -ms-user-select: none; -moz-user-select: none; user-select: none; }
.noUi-target { position: relative; margin: 0 var(--d); --d: 11px; }
.noUi-base, .noUi-connects { position: relative; z-index: 2; width: 100%; height: 100%; }
.noUi-connects { overflow: hidden; z-index: 2; }
.noUi-connect { transform: none !important; }
	[dir="rtl"] .noUi-connect { left: auto !important; }
	[dir="ltr"] .noUi-connect { right: auto !important; }
.noUi-connect, .noUi-origin { will-change: transform; position: absolute; z-index: 1; top: 0; right: 0; height: 100%; width: 100%; transform-style: preserve-3d; transform-origin: 0 0; transform-style: flat; }
.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin { left: 0; right: auto; }
.noUi-target, .noUi-connect { position: relative; }
	.noUi-target:before, .noUi-connect:before { content: ""; display: block; position: absolute; left: calc(0px - var(--d)); right: calc(0px - var(--d)); top: 50%; height: 8px; margin: -4px 0 0; border-radius: 4px; background: var(--range_bg_blank); opacity: var(--range_bg_blank_op); }
	.noUi-connect:before { left: calc(2px - var(--d)); right: calc(2px - var(--d)); background: var(--range_bg); opacity: 1; }
	.noUi-connect[style*="width: 0%"]:before, .noUi-connect[style*="width:0%"]:before { display: none; }
.noUi-vertical .noUi-origin { top: -100%; width: 0; }
.noUi-horizontal .noUi-origin { height: 0; }
.noUi-handle { position: absolute; z-index: 9; }
.noUi-handle { -webkit-backface-visibility: hidden; backface-visibility: hidden; }
.noUi-touch-area { width: 100%; height: 100%; }
.noUi-state-drag * { cursor: inherit !important; }
.noUi-horizontal { height: calc(var(--d) * 2); }
.noUi-horizontal .noUi-handle { right: calc(0px - var(--d)); top: 0; width: calc(var(--d) * 2); height: calc(var(--d) * 2); box-shadow: 0 1px 3px rgba(0,0,0,.15); border-radius: calc(var(--d) * 2); border: 1px solid var(--custom_bd); background: var(--white); line-height: 22; cursor: pointer; }
	.noUi-tooltip { display: block; overflow: hidden; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 999; padding: 0 3px; font-size: 12px; font-weight: var(--main_fw_strong); text-align: center; text-overflow: ellipsis; white-space: nowrap; }
	.noUi-horizontal .noUi-handle:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 44px; height: 44px; margin: -22px 0 0 -22px; }
.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle { left: calc(0px - var(--d)); right: auto; }
.input-range [disabled] ~ .range-inner { pointer-events: none; }

.range-cloned { display: none; }
	.range-cloned > * { display: block; overflow: hidden; position: relative; width: calc(var(--d) * 2); height: 100%; text-indent: -3000em; text-align: left; direction: ltr; }
	.range-cloned > *:not(:first-child, :last-child):before { content: ""; display: block; position: absolute; left: 50%; top: 0; bottom: 0; width: 1px; margin-left: -0.5px; background: var(--body_bg); }
.noUi-base .range-cloned { position: absolute; left: calc(0px - var(--d)); right: calc(0px - var(--d)); top: 50%; z-index: 3; height: 8px; margin-top: -4px; pointer-events: none; }

.input-range-steps { display: none; list-style: none; margin: 0; padding: 0; font-size: var(--main_fz); text-align: center; }
	.input-range.slider-is-here ~ .input-range-steps, .noUi-base .range-cloned { display: flex; justify-content: space-between; }
	.input-range-steps > * { display: block; position: relative; width: calc(var(--d) * 2); height: calc(var(--main_fz) * var(--main_lh)); white-space: nowrap; }
		.input-range-steps > *:first-child { text-align: var(--text_align_start); }
		.input-range-steps > *:last-child { text-align: var(--text_align_end); }
	.input-range-steps .inner { display: block; width: 100px; }
		.input-range-steps > *:not(:first-child, :last-child) .inner { position: absolute; left: 50%; top: 0; margin-left: -50px; /*margin-left: -39px;*/ text-align: center; }
		.input-range-steps > *:last-child .inner { float: var(--text_align_end); }

/*.input-range.compact {}*/
	.input-range.compact .noUi-target { --d: 9px; }
	.input-range.compact .noUi-target:before, .input-range.compact .noUi-connect:before { height: 4px; margin-top: -2px; }
	.input-range.compact .noUi-horizontal .noUi-handle { box-shadow: none; border-color: var(--secondary_bg); background: var(--secondary_bg); }
	

.input-range.single .noUi-connects { overflow: visible; margin-left: -12px; }
	.input-range.single .noUi-connect:before { left: 0; }
[dir="rtl"] .input-range.single .noUi-connects { margin-left: 0; margin-right: -12px; }
	[dir="rtl"] .input-range.single .noUi-connect:before { left: -12px; right: 0; }

.input-range.tip { padding-top: 30px; }
	.m6bx > .input-range.tip:first-child, .m6bx > *:first-child .input-range.tip { /*margin-top: -6px;*/ padding-top: 0; }
	.m6bx > .input-range.tip.inv:last-child, .m6bx > *:last-child .input-range.tip.inv { padding-bottom: 0; }
	.input-range.tip .noUi-target { margin-left: 0; margin-right: 0; }
		.input-range.tip .noUi-target:before, .input-range.tip .noUi-connect:before { left: 0; right: 0; }
		.input-range.tip .noUi-connects { margin-left: 0; margin-right: 0; }
	.input-range.tip .noUi-handle { width: 36px; height: 28px; top: -30px; margin: 0; border-radius: var(--b2r); border-width: 0; background: var(--range_tip_bg); color: var(--range_tip_fg); line-height: 28px; text-align: center; text-indent: 0; direction: ltr; }
		.input-range.tip .noUi-handle:before { 
			left: 50%; top: 100%; right: auto; bottom: auto; width: 6px; height: 6px; margin: -3px 0 0 -3px; background: var(--range_tip_bg);
			transform: rotate(45deg);
		}
		.input-range.tip .noUi-horizontal .noUi-handle { right: -18px; }
	.input-range.tip .noUi-origin[style*="translate(-100%"] .noUi-handle { right: -36px; }
	.input-range.tip .noUi-origin[style*="translate(0%"] .noUi-handle { right: 0; }
.input-range.tip.inv { padding-top: 0; padding-bottom: 30px; }
	.input-range.tip.inv .noUi-handle { top: auto; bottom: -52px; }
		.input-range.tip.inv .noUi-handle:before { top: auto; bottom: 100%; margin-bottom: -3px; margin-top: 0; }
		
/*.input-range.solid {}*/
	.input-range.solid .range-inner { overflow: hidden; }
	.input-range.solid .noUi-target { margin-bottom: calc(var(--main_mr) * 0.25); }
	.input-range.solid .noUi-horizontal { height: 30px; }
	.input-range.solid .noUi-target:before, .input-range.solid .noUi-connect:before { top: 0; bottom: 0; height: auto; margin: 0; }
	.input-range.solid .noUi-target:before { box-shadow: 0 1px 2px rgba(0,0,0,.06) inset; }
	#root .input-range.solid { padding: 0; }
	#root .input-range.solid .noUi-handle { overflow: visible; right: 0; top: 0; bottom: 0; width: 100px; height: 30px; margin-top: 0; margin-bottom: 0; padding: 0; box-shadow: none; border-radius: 0; background: none; line-height: 30px; text-overflow: inherit; white-space: normal; }
		#root .input-range.solid .noUi-handle:before { display: none; }
		#root .input-range.solid .noUi-tooltip { overflow: visible; padding: 0 10px; text-align: var(--text_align_end); }
			#root .input-range.solid .noUi-origin[style*="translate(-100%"] .noUi-handle .noUi-tooltip { left: var(--l1ra); right: var(--lar1); color: var(--range_fg); }

#root .input-range.tip-hidden .noUi-handle { display: none; }