
.datepicker-cell:before { content: ""; display: block; overflow: hidden; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: -1; margin: 0; text-align: left; text-indent: -3000em; direction: ltr; }

.datepicker-dropdown.active { display: block; }

.datepicker-dropdown { position: absolute; left: 0; right: 0; top: 100%; z-index: 20; padding: calc(var(--main_fz) * 1.1428571429) calc(var(--main_fz) * 1.1428571429) calc(var(--main_fz) * 1.1428571429); border-radius: var(--b2i); border: 1px solid var(--custom_input_bd); background: var(--custom_input_bg); color: var(--custom_input_fg); font-size: var(--main_fz); }

.datepicker-header { margin-bottom: calc(var(--main_fz) * 1.1428571429); } 
	.datepicker-controls { width: 100%; }
	.datepicker-header button { 
		display: block; min-width: 0; min-height: 0; margin: 0; padding: 0; box-shadow: none; border-radius: 0; border-width: 0; background: none; color: inherit !important; font-weight: var(--main_fw_strong); font-style: var(--main_fs); font-family: var(--main_ff); font-size: var(--main_fz); line-height: var(--main_lh); text-transform: var(--main_tt); letter-spacing: var(--main_ls);
		animation: none !important;
	}
		.datepicker-header button.next-button, .datepicker-header button.prev-button { width: calc(var(--main_fz) * var(--main_lh)); }
			.datepicker-header button:before { display: none; }

.datepicker-grid, .days-of-week { margin-left: -1px; }
.datepicker-cell { display: block; position: relative; z-index: 2; height: 2.25rem; cursor: pointer; }
	.datepicker-cell.prev, .datepicker-cell.next { opacity: .4; }
	.datepicker-cell:before { border-radius: var(--b2r); }
	[data-whatintent="mouse"] .datepicker-cell:hover:before, .datepicker-cell.focused:before { background: var(--custom_input_fg); opacity: .05; }
	.datepicker-cell.selected { color: var(--white); cursor: default; }
	.datepicker-cell.selected:before, [data-whatintent="mouse"] .datepicker-cell.selected:hover:before { background: var(--secondary_bg); opacity: 1; }	
	.datepicker-cell.disabled { opacity: .4; pointer-events: none; }
	
.datepicker .dow { font-weight: var(--main_fw_strong); }

.datepicker .week { flex: auto; }

.datepicker-cell, .datepicker .days .dow { 
	margin: 0 0 1px; border-left: 1px solid rgba(0,0,0,0); text-align: center;
	flex-basis: 14.2857142857%;
}
.datepicker-cell:not(.day) { 
	height: 4.25rem;
	flex-basis: 25%;
}

/*! Flexbox --------- */
/* flex */ 	.datepicker-cell, .datepicker-grid, .datepicker-header, .datepicker-controls, .days-of-week { display: flex; flex-wrap: wrap; }
/* f:wn */	.datepicker-cell, .datepicker-header, .datepicker-controls { flex-wrap: nowrap; }
/* f:jc */	.datepicker-cell { justify-content: center; }
/* f:ac */	.datepicker-cell, .datepicker-controls { align-items: center; }
/* f:g3 */	.view-switch { flex-grow: 3; }
