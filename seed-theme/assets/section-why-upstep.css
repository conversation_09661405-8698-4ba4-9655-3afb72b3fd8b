/* Why Upstep Section Styles */
.why-upstep-section {
  width: 100%;
  padding: var(--section-padding-top) 0 var(--section-padding-bottom);
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;
}

.why-upstep-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 10px 20px 0;
  box-sizing: border-box;
}

.why-upstep-content {
  display: flex;
  align-items: center;
  gap: var(--content-gap);
  min-height: 500px;
  width: 100%;
  box-sizing: border-box;
  max-width: 1400px;
  margin: 0 auto;
}

.why-upstep-text {
  flex: 0 0 50%;
  width: 50%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.why-upstep-title {
  box-sizing: border-box;
  color: var(--title-color);
  display: block;
  font-family: Merriweather_, serif;
  font-size: var(--title-size);
  font-weight: 700;
  line-height: 50.4px;
  letter-spacing: 0.12px;
  margin: 0 0 15px 0;
  width: 100%;
  max-width: 440px;
  unicode-bidi: isolate;
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  outline-color: var(--title-color);
  outline-style: none;
  outline-width: 0px;
}

.why-upstep-description {
  box-sizing: border-box;
  color: var(--description-color);
  display: block;
  font-family: Lato_, sans-serif;
  font-size: var(--description-size);
  line-height: 22.4px;
  letter-spacing: 0.0278261px;
  margin: 0 0 30px 0;
  width: 100%;
  max-width: 385px;
  unicode-bidi: isolate;
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  outline-color: var(--description-color);
  outline-style: none;
  outline-width: 0px;
}

.why-upstep-description p {
  margin: 0 0 15px 0;
}

.why-upstep-description p:last-child {
  margin-bottom: 0;
}

.why-upstep-button-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 12px;
}

/* Button styles - exact copy from provided styles */
.why-upstep-btn {
  appearance: button;
  background-color: rgb(0, 158, 224);
  border: 1px solid rgb(0, 158, 224);
  border-radius: 4px;
  box-shadow: rgba(0, 158, 224, 0.4) 0px 4px 16px 0px;
  box-sizing: border-box;
  color: rgb(255, 255, 255);
  cursor: pointer;
  display: inline-block;
  font-family: Lato_, sans-serif;
  font-size: 16px;
  font-weight: 600;
  height: 50px;
  letter-spacing: 0.571429px;
  line-height: 24px;
  min-width: 200px;
  padding: 12px 30px;
  position: relative;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.2s linear;
  vertical-align: middle;
  width: 200px;
  white-space: nowrap;
  outline: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.why-upstep-btn:hover {
  background-color: rgb(20, 178, 244);
  border-color: rgb(20, 178, 244);
  color: rgb(255, 255, 255);
  text-decoration: none;
}

.why-upstep-btn:focus {
  outline: 2px solid rgb(0, 158, 224);
  outline-offset: 2px;
}

.why-upstep-btn:active {
  background-color: rgb(0, 138, 196);
  border-color: rgb(0, 138, 196);
}

.why-upstep-btn[aria-disabled="true"] {
  background-color: #ccc;
  border-color: #ccc;
  color: #666;
  cursor: not-allowed;
  pointer-events: none;
}

/* Guarantee text */
.why-upstep-guarantee {
  box-sizing: border-box;
  color: var(--guarantee-color);
  display: block;
  font-family: Lato_, sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 22.4px;
  margin: 0;
  max-width: 230px;
  text-align: left;
  unicode-bidi: isolate;
  width: auto;
}

.why-upstep-image {
  flex: 0 0 50%;
  width: 50%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.why-upstep-img {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.why-upstep-svg-container {
  width: 100%;
  max-width: 650px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.why-upstep-svg-container svg {
  max-width: 100%;
  height: auto;
  display: block;
  transition: all 0.3s ease;
}

.why-upstep-image-placeholder {
  width: 100%;
  max-width: 650px;
  height: 500px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-family: Lato_, sans-serif;
  font-size: 14px;
  text-align: center;
  box-sizing: border-box;
}

.why-upstep-image-placeholder svg {
  margin-bottom: 15px;
  opacity: 0.6;
}

.why-upstep-image-placeholder p {
  margin: 0;
  font-weight: 500;
}

/* Large Desktop Styles */
@media (min-width: 1440px) {
  .why-upstep-content {
    max-width: 1600px;
    gap: 0px;
    min-height: 450px;
  }
  
  .why-upstep-title {
    font-size: calc(var(--title-size) + 6px);
  }
  
  .why-upstep-description {
    font-size: calc(var(--description-size) + 2px);
  }
  
  .why-upstep-btn {
    font-size: 18px;
    padding: 14px 28px;
    min-width: 220px;
    width: 220px;
  }
  
  .why-upstep-svg-container {
    max-width: 750px;
  }
}

/* Desktop Styles */
@media (max-width: 1439px) and (min-width: 1025px) {
  .why-upstep-content {
    max-width: 1400px;
    gap: var(--content-gap);
    min-height: 400px;
  }
}

/* Tablet Styles */
@media (max-width: 1024px) and (min-width: 769px) {
  .why-upstep-content {
    gap: 40px;
    min-height: 350px;
  }
  
  .why-upstep-text {
    flex: 0 0 50%;
    width: 50%;
  }
  
  .why-upstep-image {
    flex: 0 0 50%;
    width: 50%;
  }
  
  .why-upstep-title {
    font-size: calc(var(--title-size) - 4px);
    max-width: 100%;
  }
  
  .why-upstep-description {
    font-size: calc(var(--description-size) - 1px);
    max-width: 100%;
  }
  
  .why-upstep-btn {
    padding: 12px 25px;
    font-size: 15px;
  }
  
  .why-upstep-guarantee {
    font-size: 13px;
  }
  
  .why-upstep-svg-container {
    max-width: 550px;
  }
}

/* Mobile Landscape Styles */
@media (max-width: 768px) and (min-width: 481px) {
  .why-upstep-section {
    padding: var(--section-padding-top-mobile) 0 var(--section-padding-bottom-mobile);
  }
  
  .why-upstep-content {
    flex-direction: column;
    gap: var(--content-gap-mobile);
    min-height: 300px;
  }
  
  .why-upstep-text {
    flex: none;
    width: 100%;
    padding-left: 0;
    text-align: center;
  }
  
  .why-upstep-title {
    font-size: var(--title-size-mobile);
    max-width: 100%;
    margin: 0 0 15px 0;
  }
  
  .why-upstep-description {
    font-size: var(--description-size-mobile);
    max-width: 100%;
    margin: 0 0 20px 0;
  }
  
  .why-upstep-button-container {
    align-items: center;
    margin-top: 15px;
  }
  
  .why-upstep-btn {
    width: 100%;
    max-width: 250px;
    padding: 12px 20px;
    font-size: 14px;
  }
  
  .why-upstep-guarantee {
    font-size: 12px;
  }
  
  .why-upstep-image {
    flex: none;
    width: 100%;
    order: -1;
  }
  
  .why-upstep-image-placeholder {
    max-width: 100%;
    height: 300px;
  }
  
  .why-upstep-svg-container {
    max-width: 100%;
  }
}

/* Mobile Portrait Styles */
@media (max-width: 480px) {
  .why-upstep-section {
    padding: var(--section-padding-top-mobile) 0 var(--section-padding-bottom-mobile);
  }
  
  .why-upstep-container {
    padding: 0 15px;
  }
  
  .why-upstep-content {
    flex-direction: column;
    gap: var(--content-gap-mobile);
    min-height: 250px;
  }
  
  .why-upstep-text {
    flex: none;
    width: 100%;
    padding-left: 0;
    text-align: center;
  }
  
  .why-upstep-title {
    font-size: var(--title-size-mobile);
    max-width: 100%;
    margin: 0 0 15px 0;
    line-height: 1.3;
  }
  
  .why-upstep-description {
    font-size: var(--description-size-mobile);
    max-width: 100%;
    margin: 0 0 20px 0;
    line-height: 1.4;
  }
  
  .why-upstep-button-container {
    align-items: center;
    margin-top: 15px;
  }
  
  .why-upstep-btn {
    width: 100%;
    max-width: 200px;
    padding: 12px 20px;
    font-size: 14px;
  }
  
  .why-upstep-guarantee {
    font-size: 12px;
    text-align: center;
  }
  
  .why-upstep-image {
    flex: none;
    width: 100%;
    order: -1;
  }
  
  .why-upstep-image-placeholder {
    max-width: 100%;
    height: 250px;
  }
  
  .why-upstep-svg-container {
    max-width: 100%;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .why-upstep-img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .why-upstep-btn,
  .why-upstep-img {
    transition: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .why-upstep-title {
    color: #000;
  }
  
  .why-upstep-description {
    color: #333;
  }
  
  .why-upstep-btn {
    border: 2px solid currentColor;
  }
  
  .why-upstep-guarantee {
    color: #555;
  }
}

/* Print Styles */
@media print {
  .why-upstep-section {
    background: white !important;
    color: black !important;
  }
  
  .why-upstep-btn {
    border: 1px solid black !important;
    background: white !important;
    color: black !important;
  }
  
  .why-upstep-image {
    break-inside: avoid;
  }
}

/* Focus Styles for Accessibility */
.why-upstep-btn:focus-visible {
  outline: 2px solid var(--button-bg);
  outline-offset: 2px;
}

.why-upstep-title:focus-visible,
.why-upstep-description:focus-visible {
  outline: 2px solid var(--title-color);
  outline-offset: 2px;
}

.why-upstep-image:focus-visible {
  outline: 2px solid var(--title-color);
  outline-offset: 2px;
}

/* RTL Support */
[dir="rtl"] .why-upstep-text {
  padding-left: 0;
  padding-right: 15px;
}

[dir="rtl"] .why-upstep-content {
  flex-direction: row-reverse;
}

@media (max-width: 768px) {
  [dir="rtl"] .why-upstep-content {
    flex-direction: column;
  }
  
  [dir="rtl"] .why-upstep-text {
    padding-right: 0;
  }
} 