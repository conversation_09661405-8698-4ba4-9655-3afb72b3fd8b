<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer Logo Preview</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .preview-header {
            background: #333;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .footer-preview {
            background: #f8f9fa;
            padding: 40px 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-content {
            display: flex;
            flex-wrap: wrap;
            gap: 40px;
            max-width: 1100px;
            margin: 0 auto;
        }
        
        .footer-column {
            flex: 1;
            min-width: 250px;
        }
        
        /* Footer Logo Styles */
        .footer-logo-block {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .footer-logo-block a {
            display: inline-block;
            text-decoration: none;
        }

        .footer-logo-image {
            max-width: 100%;
            height: auto;
            display: block;
        }

        .footer-logo-placeholder {
            border-radius: 4px;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
            background-color: #f0f0f0;
        }
        
        .controls {
            background: #fff;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .control-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 12px;
            color: #333;
        }
        
        .control-group input, .control-group select {
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .control-group input[type="range"] {
            width: 100px;
        }
        
        .control-group input[type="color"] {
            width: 40px;
            height: 30px;
            padding: 0;
            border: none;
        }
        
        .other-content {
            color: #666;
        }
        
        .other-content h5 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .other-content ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .other-content li {
            margin-bottom: 8px;
        }
        
        .other-content a {
            color: #666;
            text-decoration: none;
        }
        
        .other-content a:hover {
            color: #333;
        }
        
        @media (max-width: 768px) {
            .footer-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .control-group {
                display: block;
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            Footer Logo Preview - Customizable Logo Section
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>Logo Width:</label>
                <input type="range" id="logoWidth" min="50" max="300" value="120" step="10">
                <span id="logoWidthValue">120px</span>
            </div>
            
            <div class="control-group">
                <label>Horizontal Position:</label>
                <input type="range" id="logoOffsetX" min="-100" max="100" value="0" step="5">
                <span id="logoOffsetXValue">0px</span>
            </div>
            
            <div class="control-group">
                <label>Vertical Position:</label>
                <input type="range" id="logoOffsetY" min="-50" max="50" value="0" step="5">
                <span id="logoOffsetYValue">0px</span>
            </div>
            
            <div class="control-group">
                <label>Placeholder Color:</label>
                <input type="color" id="placeholderColor" value="#f0f0f0">
            </div>
            
            <div class="control-group">
                <label>Show:</label>
                <select id="logoType">
                    <option value="image">Logo Image</option>
                    <option value="placeholder">Placeholder</option>
                </select>
            </div>
        </div>
        
        <div class="footer-preview">
            <div class="footer-content">
                <!-- Logo Column -->
                <div class="footer-column">
                    <div class="footer-logo-block">
                        <a href="/">
                            <img id="logoImage" 
                                 src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 120 60'%3E%3Crect width='120' height='60' fill='%234A90E2'/%3E%3Ctext x='60' y='35' text-anchor='middle' fill='white' font-family='Arial' font-size='16' font-weight='bold'%3EUPSTEP%3C/text%3E%3C/svg%3E"
                                 alt="UPSTEP Logo"
                                 class="footer-logo-image"
                                 style="width: 120px; transform: translate(0px, 0px);">
                            <div id="logoPlaceholder" 
                                 class="footer-logo-placeholder" 
                                 style="width: 120px; height: 60px; background-color: #f0f0f0; transform: translate(0px, 0px); display: none;">
                                UPSTEP
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- Company Column -->
                <div class="footer-column other-content">
                    <h5>Company</h5>
                    <ul>
                        <li><a href="#">About</a></li>
                        <li><a href="#">Reviews</a></li>
                        <li><a href="#">Careers</a></li>
                    </ul>
                </div>
                
                <!-- Education Column -->
                <div class="footer-column other-content">
                    <h5>Education</h5>
                    <ul>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Community</a></li>
                        <li><a href="#">Resources</a></li>
                    </ul>
                </div>
                
                <!-- Support Column -->
                <div class="footer-column other-content">
                    <h5>Support</h5>
                    <ul>
                        <li><a href="#">Help</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Returns</a></li>
                    </ul>
                </div>
                
                <!-- Newsletter Column -->
                <div class="footer-column other-content">
                    <h5>Step with us! and get all our offers and benefits straight to your email!</h5>
                    <div style="display: flex; gap: 10px; margin-top: 15px;">
                        <input type="email" placeholder="Email" style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <button style="padding: 8px 16px; background: #4A90E2; color: white; border: none; border-radius: 4px; cursor: pointer;">SUBMIT</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const logoImage = document.getElementById('logoImage');
        const logoPlaceholder = document.getElementById('logoPlaceholder');
        const logoWidthSlider = document.getElementById('logoWidth');
        const logoOffsetXSlider = document.getElementById('logoOffsetX');
        const logoOffsetYSlider = document.getElementById('logoOffsetY');
        const placeholderColorPicker = document.getElementById('placeholderColor');
        const logoTypeSelect = document.getElementById('logoType');
        
        const logoWidthValue = document.getElementById('logoWidthValue');
        const logoOffsetXValue = document.getElementById('logoOffsetXValue');
        const logoOffsetYValue = document.getElementById('logoOffsetYValue');

        function updateLogo() {
            const width = logoWidthSlider.value;
            const offsetX = logoOffsetXSlider.value;
            const offsetY = logoOffsetYSlider.value;
            const color = placeholderColorPicker.value;
            const type = logoTypeSelect.value;
            
            logoWidthValue.textContent = width + 'px';
            logoOffsetXValue.textContent = offsetX + 'px';
            logoOffsetYValue.textContent = offsetY + 'px';
            
            const transform = `translate(${offsetX}px, ${offsetY}px)`;
            
            if (type === 'image') {
                logoImage.style.display = 'block';
                logoPlaceholder.style.display = 'none';
                logoImage.style.width = width + 'px';
                logoImage.style.transform = transform;
            } else {
                logoImage.style.display = 'none';
                logoPlaceholder.style.display = 'flex';
                logoPlaceholder.style.width = width + 'px';
                logoPlaceholder.style.backgroundColor = color;
                logoPlaceholder.style.transform = transform;
            }
        }

        logoWidthSlider.addEventListener('input', updateLogo);
        logoOffsetXSlider.addEventListener('input', updateLogo);
        logoOffsetYSlider.addEventListener('input', updateLogo);
        placeholderColorPicker.addEventListener('input', updateLogo);
        logoTypeSelect.addEventListener('change', updateLogo);
        
        // Initialize
        updateLogo();
    </script>
</body>
</html>
